{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "/usr/bin/cmake", "cpack": "/usr/bin/cpack", "ctest": "/usr/bin/ctest", "root": "/usr/share/cmake-3.22"}, "version": {"isDirty": false, "major": 3, "minor": 22, "patch": 1, "string": "3.22.1", "suffix": ""}}, "objects": [{"jsonFile": "codemodel-v2-6e797a2d10f6a05f7ab6.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}, {"jsonFile": "cache-v2-ea2669b386e532b8ac8e.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-c9cbe2ad6775f8338175.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, {"jsonFile": "toolchains-v1-78e1ec32f5d5e8dda251.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}], "reply": {"client-integration-vscode": {"cache-v2": {"jsonFile": "cache-v2-ea2669b386e532b8ac8e.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-c9cbe2ad6775f8338175.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, "codemodel-v2": {"jsonFile": "codemodel-v2-6e797a2d10f6a05f7ab6.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}}, "client-vscode": {"query.json": {"requests": [{"kind": "cache", "version": 2}, {"kind": "codemodel", "version": 2}, {"kind": "toolchains", "version": 1}, {"kind": "cmakeFiles", "version": 1}], "responses": [{"jsonFile": "cache-v2-ea2669b386e532b8ac8e.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "codemodel-v2-6e797a2d10f6a05f7ab6.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}, {"jsonFile": "toolchains-v1-78e1ec32f5d5e8dda251.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-c9cbe2ad6775f8338175.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}]}}}}