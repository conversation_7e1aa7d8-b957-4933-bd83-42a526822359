{"artifacts": [{"path": "bin/Pythia_EEC_intra_jet_log"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_executable", "link_directories", "target_link_libraries", "include_directories"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 62, "parent": 0}, {"command": 1, "file": 0, "line": 46, "parent": 0}, {"command": 2, "file": 0, "line": 63, "parent": 0}, {"command": 3, "file": 0, "line": 37, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-g"}, {"fragment": "-std=gnu++17"}], "includes": [{"backtrace": 4, "path": "/home/<USER>/Project/HEPLib/GSL/include"}, {"backtrace": 4, "path": "/home/<USER>/Project/HEPLib/Cuba/include"}, {"backtrace": 4, "path": "/home/<USER>/Project/HEPLib/Pythia/include"}, {"backtrace": 4, "path": "/home/<USER>/Project/HEPLib/LHAPDF/include"}, {"backtrace": 4, "path": "/home/<USER>/Project/HEPLib/FastJet/include"}, {"backtrace": 4, "path": "/home/<USER>/Project/C/EC/include"}], "language": "CXX", "languageStandard": {"backtraces": [1], "standard": "17"}, "sourceIndexes": [0, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20]}], "id": "Pythia_EEC_intra_jet_log::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"fragment": "-g", "role": "flags"}, {"fragment": "", "role": "flags"}, {"backtrace": 2, "fragment": "-L/home/<USER>/Project/HEPLib/GSL/lib", "role": "libraryPath"}, {"backtrace": 2, "fragment": "-L/home/<USER>/Project/HEPLib/Cuba/lib", "role": "libraryPath"}, {"backtrace": 2, "fragment": "-L/home/<USER>/Project/HEPLib/Pythia/lib", "role": "libraryPath"}, {"backtrace": 2, "fragment": "-L/home/<USER>/Project/HEPLib/LHAPDF/lib", "role": "libraryPath"}, {"backtrace": 2, "fragment": "-L/home/<USER>/Project/HEPLib/FastJet/lib", "role": "libraryPath"}, {"fragment": "-Wl,-rpath,/home/<USER>/Project/HEPLib/GSL/lib:/home/<USER>/Project/HEPLib/Cuba/lib:/home/<USER>/Project/HEPLib/Pythia/lib:/home/<USER>/Project/HEPLib/LHAPDF/lib:/home/<USER>/Project/HEPLib/FastJet/lib", "role": "libraries"}, {"backtrace": 3, "fragment": "-lpythia8", "role": "libraries"}, {"backtrace": 3, "fragment": "-lfastjet", "role": "libraries"}, {"backtrace": 3, "fragment": "-lz", "role": "libraries"}, {"backtrace": 3, "fragment": "-lgsl", "role": "libraries"}, {"backtrace": 3, "fragment": "-lcuba", "role": "libraries"}, {"backtrace": 3, "fragment": "-lLHAPDF", "role": "libraries"}, {"backtrace": 3, "fragment": "-lgfortran", "role": "libraries"}, {"backtrace": 3, "fragment": "-lgslcblas", "role": "libraries"}], "language": "CXX"}, "name": "Pythia_EEC_intra_jet_log", "nameOnDisk": "Pythia_EEC_intra_jet_log", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20]}, {"name": "<PERSON><PERSON>", "sourceIndexes": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "target/pythia/Pythia_EEC_intra_jet_log.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "include/EC.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "include/amplitude.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "include/basic_function.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "include/bin_generator.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "include/jet.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "include/jet_function.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "include/phase_space.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "include/process_data.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "include/run_parameters.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "include/vegas_parameters.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/EC.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/amplitude.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/basic_function.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/bin_generator.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/jet.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/jet_function.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/phase_space.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/process_data.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/run_parameters.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "src/vegas_parameters.cpp", "sourceGroupIndex": 0}], "type": "EXECUTABLE"}