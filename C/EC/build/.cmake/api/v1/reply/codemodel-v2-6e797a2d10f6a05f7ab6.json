{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-Debug-f5ebdc15457944623624.json", "minimumCMakeVersion": {"string": "3.10"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12]}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "EEC", "targetIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12]}], "targets": [{"directoryIndex": 0, "id": "EC_resummation::@6890427a1f51a3e7e1df", "jsonFile": "target-EC_resummation-Debug-078c3451f77aca6ca812.json", "name": "EC_resummation", "projectIndex": 0}, {"directoryIndex": 0, "id": "Pythia_EC_FO::@6890427a1f51a3e7e1df", "jsonFile": "target-Pythia_EC_FO-Debug-6b69b4e9366972cab761.json", "name": "Pythia_EC_FO", "projectIndex": 0}, {"directoryIndex": 0, "id": "Pythia_EC_resum::@6890427a1f51a3e7e1df", "jsonFile": "target-Pythia_EC_resum-Debug-8239338d21c808094389.json", "name": "Pythia_EC_resum", "projectIndex": 0}, {"directoryIndex": 0, "id": "Pythia_EEC_EPA::@6890427a1f51a3e7e1df", "jsonFile": "target-Pythia_EEC_EPA-Debug-2a1ef9ee25ecd0abe648.json", "name": "Pythia_EEC_EPA", "projectIndex": 0}, {"directoryIndex": 0, "id": "Pythia_EEC_Jet::@6890427a1f51a3e7e1df", "jsonFile": "target-Pythia_EEC_Jet-Debug-500fd1bfde4d899d8a30.json", "name": "Pythia_EEC_Jet", "projectIndex": 0}, {"directoryIndex": 0, "id": "Pythia_EEC_intra_jet::@6890427a1f51a3e7e1df", "jsonFile": "target-Pythia_EEC_intra_jet-Debug-14d172491ec4678bf5f5.json", "name": "Pythia_EEC_intra_jet", "projectIndex": 0}, {"directoryIndex": 0, "id": "Pythia_EEC_intra_jet_CMS::@6890427a1f51a3e7e1df", "jsonFile": "target-Pythia_EEC_intra_jet_CMS-Debug-50d2d672ca3657d7a8f1.json", "name": "Pythia_EEC_intra_jet_CMS", "projectIndex": 0}, {"directoryIndex": 0, "id": "Pythia_EEC_intra_jet_log::@6890427a1f51a3e7e1df", "jsonFile": "target-Pythia_EEC_intra_jet_log-Debug-2b24ed7607f21af4dfdb.json", "name": "Pythia_EEC_intra_jet_log", "projectIndex": 0}, {"directoryIndex": 0, "id": "Pythia_MG5_EEC_e-e+::@6890427a1f51a3e7e1df", "jsonFile": "target-Pythia_MG5_EEC_e-e+-Debug-1b20eb19c44f8288ff8a.json", "name": "Pythia_MG5_EEC_e-e+", "projectIndex": 0}, {"directoryIndex": 0, "id": "Pythia_one_inclusive::@6890427a1f51a3e7e1df", "jsonFile": "target-Pythia_one_inclusive-Debug-ab0ae43f436b06dcd3a7.json", "name": "Pythia_one_inclusive", "projectIndex": 0}, {"directoryIndex": 0, "id": "Pythia_one_jet_inclusive::@6890427a1f51a3e7e1df", "jsonFile": "target-Pythia_one_jet_inclusive-Debug-3b8e7efb54192c628177.json", "name": "Pythia_one_jet_inclusive", "projectIndex": 0}, {"directoryIndex": 0, "id": "cross::@6890427a1f51a3e7e1df", "jsonFile": "target-cross-Debug-9de0f17b2b2fd056ba32.json", "name": "cross", "projectIndex": 0}, {"directoryIndex": 0, "id": "eec::@6890427a1f51a3e7e1df", "jsonFile": "target-eec-Debug-d3ea14584596e8e6da3b.json", "name": "eec", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "/home/<USER>/Project/C/EC/build", "source": "/home/<USER>/Project/C/EC"}, "version": {"major": 2, "minor": 3}}