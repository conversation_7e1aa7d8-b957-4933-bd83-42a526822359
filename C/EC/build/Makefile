# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/Project/C

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/Project/C/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Project/C/build/CMakeFiles /home/<USER>/Project/C/build//CMakeFiles/progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/Project/C/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named EC_resummation

# Build rule for target.
EC_resummation: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 EC_resummation
.PHONY : EC_resummation

# fast build rule for target.
EC_resummation/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/EC_resummation.dir/build.make CMakeFiles/EC_resummation.dir/build
.PHONY : EC_resummation/fast

#=============================================================================
# Target rules for targets named cross

# Build rule for target.
cross: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 cross
.PHONY : cross

# fast build rule for target.
cross/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cross.dir/build.make CMakeFiles/cross.dir/build
.PHONY : cross/fast

#=============================================================================
# Target rules for targets named Pythia_EC_FO

# Build rule for target.
Pythia_EC_FO: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 Pythia_EC_FO
.PHONY : Pythia_EC_FO

# fast build rule for target.
Pythia_EC_FO/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_EC_FO.dir/build.make CMakeFiles/Pythia_EC_FO.dir/build
.PHONY : Pythia_EC_FO/fast

#=============================================================================
# Target rules for targets named Pythia_EC_resum

# Build rule for target.
Pythia_EC_resum: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 Pythia_EC_resum
.PHONY : Pythia_EC_resum

# fast build rule for target.
Pythia_EC_resum/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_EC_resum.dir/build.make CMakeFiles/Pythia_EC_resum.dir/build
.PHONY : Pythia_EC_resum/fast

#=============================================================================
# Target rules for targets named Pythia_MG5_EEC_e-e+

# Build rule for target.
Pythia_MG5_EEC_e-e+: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 Pythia_MG5_EEC_e-e+
.PHONY : Pythia_MG5_EEC_e-e+

# fast build rule for target.
Pythia_MG5_EEC_e-e+/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_MG5_EEC_e-e+.dir/build.make CMakeFiles/Pythia_MG5_EEC_e-e+.dir/build
.PHONY : Pythia_MG5_EEC_e-e+/fast

#=============================================================================
# Target rules for targets named Pythia_one_inclusive

# Build rule for target.
Pythia_one_inclusive: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 Pythia_one_inclusive
.PHONY : Pythia_one_inclusive

# fast build rule for target.
Pythia_one_inclusive/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_one_inclusive.dir/build.make CMakeFiles/Pythia_one_inclusive.dir/build
.PHONY : Pythia_one_inclusive/fast

#=============================================================================
# Target rules for targets named Pythia_one_jet_inclusive

# Build rule for target.
Pythia_one_jet_inclusive: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 Pythia_one_jet_inclusive
.PHONY : Pythia_one_jet_inclusive

# fast build rule for target.
Pythia_one_jet_inclusive/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_one_jet_inclusive.dir/build.make CMakeFiles/Pythia_one_jet_inclusive.dir/build
.PHONY : Pythia_one_jet_inclusive/fast

src/EC.o: src/EC.cpp.o
.PHONY : src/EC.o

# target to build an object file
src/EC.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/EC_resummation.dir/build.make CMakeFiles/EC_resummation.dir/src/EC.cpp.o
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cross.dir/build.make CMakeFiles/cross.dir/src/EC.cpp.o
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_EC_FO.dir/build.make CMakeFiles/Pythia_EC_FO.dir/src/EC.cpp.o
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_EC_resum.dir/build.make CMakeFiles/Pythia_EC_resum.dir/src/EC.cpp.o
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_MG5_EEC_e-e+.dir/build.make CMakeFiles/Pythia_MG5_EEC_e-e+.dir/src/EC.cpp.o
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_one_inclusive.dir/build.make CMakeFiles/Pythia_one_inclusive.dir/src/EC.cpp.o
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_one_jet_inclusive.dir/build.make CMakeFiles/Pythia_one_jet_inclusive.dir/src/EC.cpp.o
.PHONY : src/EC.cpp.o

src/EC.i: src/EC.cpp.i
.PHONY : src/EC.i

# target to preprocess a source file
src/EC.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/EC_resummation.dir/build.make CMakeFiles/EC_resummation.dir/src/EC.cpp.i
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cross.dir/build.make CMakeFiles/cross.dir/src/EC.cpp.i
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_EC_FO.dir/build.make CMakeFiles/Pythia_EC_FO.dir/src/EC.cpp.i
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_EC_resum.dir/build.make CMakeFiles/Pythia_EC_resum.dir/src/EC.cpp.i
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_MG5_EEC_e-e+.dir/build.make CMakeFiles/Pythia_MG5_EEC_e-e+.dir/src/EC.cpp.i
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_one_inclusive.dir/build.make CMakeFiles/Pythia_one_inclusive.dir/src/EC.cpp.i
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_one_jet_inclusive.dir/build.make CMakeFiles/Pythia_one_jet_inclusive.dir/src/EC.cpp.i
.PHONY : src/EC.cpp.i

src/EC.s: src/EC.cpp.s
.PHONY : src/EC.s

# target to generate assembly for a file
src/EC.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/EC_resummation.dir/build.make CMakeFiles/EC_resummation.dir/src/EC.cpp.s
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cross.dir/build.make CMakeFiles/cross.dir/src/EC.cpp.s
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_EC_FO.dir/build.make CMakeFiles/Pythia_EC_FO.dir/src/EC.cpp.s
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_EC_resum.dir/build.make CMakeFiles/Pythia_EC_resum.dir/src/EC.cpp.s
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_MG5_EEC_e-e+.dir/build.make CMakeFiles/Pythia_MG5_EEC_e-e+.dir/src/EC.cpp.s
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_one_inclusive.dir/build.make CMakeFiles/Pythia_one_inclusive.dir/src/EC.cpp.s
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_one_jet_inclusive.dir/build.make CMakeFiles/Pythia_one_jet_inclusive.dir/src/EC.cpp.s
.PHONY : src/EC.cpp.s

src/amplitude.o: src/amplitude.cpp.o
.PHONY : src/amplitude.o

# target to build an object file
src/amplitude.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/EC_resummation.dir/build.make CMakeFiles/EC_resummation.dir/src/amplitude.cpp.o
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cross.dir/build.make CMakeFiles/cross.dir/src/amplitude.cpp.o
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_EC_FO.dir/build.make CMakeFiles/Pythia_EC_FO.dir/src/amplitude.cpp.o
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_EC_resum.dir/build.make CMakeFiles/Pythia_EC_resum.dir/src/amplitude.cpp.o
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_MG5_EEC_e-e+.dir/build.make CMakeFiles/Pythia_MG5_EEC_e-e+.dir/src/amplitude.cpp.o
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_one_inclusive.dir/build.make CMakeFiles/Pythia_one_inclusive.dir/src/amplitude.cpp.o
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_one_jet_inclusive.dir/build.make CMakeFiles/Pythia_one_jet_inclusive.dir/src/amplitude.cpp.o
.PHONY : src/amplitude.cpp.o

src/amplitude.i: src/amplitude.cpp.i
.PHONY : src/amplitude.i

# target to preprocess a source file
src/amplitude.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/EC_resummation.dir/build.make CMakeFiles/EC_resummation.dir/src/amplitude.cpp.i
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cross.dir/build.make CMakeFiles/cross.dir/src/amplitude.cpp.i
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_EC_FO.dir/build.make CMakeFiles/Pythia_EC_FO.dir/src/amplitude.cpp.i
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_EC_resum.dir/build.make CMakeFiles/Pythia_EC_resum.dir/src/amplitude.cpp.i
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_MG5_EEC_e-e+.dir/build.make CMakeFiles/Pythia_MG5_EEC_e-e+.dir/src/amplitude.cpp.i
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_one_inclusive.dir/build.make CMakeFiles/Pythia_one_inclusive.dir/src/amplitude.cpp.i
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_one_jet_inclusive.dir/build.make CMakeFiles/Pythia_one_jet_inclusive.dir/src/amplitude.cpp.i
.PHONY : src/amplitude.cpp.i

src/amplitude.s: src/amplitude.cpp.s
.PHONY : src/amplitude.s

# target to generate assembly for a file
src/amplitude.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/EC_resummation.dir/build.make CMakeFiles/EC_resummation.dir/src/amplitude.cpp.s
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cross.dir/build.make CMakeFiles/cross.dir/src/amplitude.cpp.s
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_EC_FO.dir/build.make CMakeFiles/Pythia_EC_FO.dir/src/amplitude.cpp.s
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_EC_resum.dir/build.make CMakeFiles/Pythia_EC_resum.dir/src/amplitude.cpp.s
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_MG5_EEC_e-e+.dir/build.make CMakeFiles/Pythia_MG5_EEC_e-e+.dir/src/amplitude.cpp.s
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_one_inclusive.dir/build.make CMakeFiles/Pythia_one_inclusive.dir/src/amplitude.cpp.s
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_one_jet_inclusive.dir/build.make CMakeFiles/Pythia_one_jet_inclusive.dir/src/amplitude.cpp.s
.PHONY : src/amplitude.cpp.s

src/basic_function.o: src/basic_function.cpp.o
.PHONY : src/basic_function.o

# target to build an object file
src/basic_function.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/EC_resummation.dir/build.make CMakeFiles/EC_resummation.dir/src/basic_function.cpp.o
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cross.dir/build.make CMakeFiles/cross.dir/src/basic_function.cpp.o
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_EC_FO.dir/build.make CMakeFiles/Pythia_EC_FO.dir/src/basic_function.cpp.o
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_EC_resum.dir/build.make CMakeFiles/Pythia_EC_resum.dir/src/basic_function.cpp.o
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_MG5_EEC_e-e+.dir/build.make CMakeFiles/Pythia_MG5_EEC_e-e+.dir/src/basic_function.cpp.o
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_one_inclusive.dir/build.make CMakeFiles/Pythia_one_inclusive.dir/src/basic_function.cpp.o
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_one_jet_inclusive.dir/build.make CMakeFiles/Pythia_one_jet_inclusive.dir/src/basic_function.cpp.o
.PHONY : src/basic_function.cpp.o

src/basic_function.i: src/basic_function.cpp.i
.PHONY : src/basic_function.i

# target to preprocess a source file
src/basic_function.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/EC_resummation.dir/build.make CMakeFiles/EC_resummation.dir/src/basic_function.cpp.i
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cross.dir/build.make CMakeFiles/cross.dir/src/basic_function.cpp.i
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_EC_FO.dir/build.make CMakeFiles/Pythia_EC_FO.dir/src/basic_function.cpp.i
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_EC_resum.dir/build.make CMakeFiles/Pythia_EC_resum.dir/src/basic_function.cpp.i
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_MG5_EEC_e-e+.dir/build.make CMakeFiles/Pythia_MG5_EEC_e-e+.dir/src/basic_function.cpp.i
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_one_inclusive.dir/build.make CMakeFiles/Pythia_one_inclusive.dir/src/basic_function.cpp.i
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_one_jet_inclusive.dir/build.make CMakeFiles/Pythia_one_jet_inclusive.dir/src/basic_function.cpp.i
.PHONY : src/basic_function.cpp.i

src/basic_function.s: src/basic_function.cpp.s
.PHONY : src/basic_function.s

# target to generate assembly for a file
src/basic_function.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/EC_resummation.dir/build.make CMakeFiles/EC_resummation.dir/src/basic_function.cpp.s
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cross.dir/build.make CMakeFiles/cross.dir/src/basic_function.cpp.s
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_EC_FO.dir/build.make CMakeFiles/Pythia_EC_FO.dir/src/basic_function.cpp.s
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_EC_resum.dir/build.make CMakeFiles/Pythia_EC_resum.dir/src/basic_function.cpp.s
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_MG5_EEC_e-e+.dir/build.make CMakeFiles/Pythia_MG5_EEC_e-e+.dir/src/basic_function.cpp.s
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_one_inclusive.dir/build.make CMakeFiles/Pythia_one_inclusive.dir/src/basic_function.cpp.s
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_one_jet_inclusive.dir/build.make CMakeFiles/Pythia_one_jet_inclusive.dir/src/basic_function.cpp.s
.PHONY : src/basic_function.cpp.s

src/bin_generator.o: src/bin_generator.cpp.o
.PHONY : src/bin_generator.o

# target to build an object file
src/bin_generator.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/EC_resummation.dir/build.make CMakeFiles/EC_resummation.dir/src/bin_generator.cpp.o
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cross.dir/build.make CMakeFiles/cross.dir/src/bin_generator.cpp.o
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_EC_FO.dir/build.make CMakeFiles/Pythia_EC_FO.dir/src/bin_generator.cpp.o
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_EC_resum.dir/build.make CMakeFiles/Pythia_EC_resum.dir/src/bin_generator.cpp.o
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_MG5_EEC_e-e+.dir/build.make CMakeFiles/Pythia_MG5_EEC_e-e+.dir/src/bin_generator.cpp.o
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_one_inclusive.dir/build.make CMakeFiles/Pythia_one_inclusive.dir/src/bin_generator.cpp.o
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_one_jet_inclusive.dir/build.make CMakeFiles/Pythia_one_jet_inclusive.dir/src/bin_generator.cpp.o
.PHONY : src/bin_generator.cpp.o

src/bin_generator.i: src/bin_generator.cpp.i
.PHONY : src/bin_generator.i

# target to preprocess a source file
src/bin_generator.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/EC_resummation.dir/build.make CMakeFiles/EC_resummation.dir/src/bin_generator.cpp.i
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cross.dir/build.make CMakeFiles/cross.dir/src/bin_generator.cpp.i
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_EC_FO.dir/build.make CMakeFiles/Pythia_EC_FO.dir/src/bin_generator.cpp.i
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_EC_resum.dir/build.make CMakeFiles/Pythia_EC_resum.dir/src/bin_generator.cpp.i
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_MG5_EEC_e-e+.dir/build.make CMakeFiles/Pythia_MG5_EEC_e-e+.dir/src/bin_generator.cpp.i
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_one_inclusive.dir/build.make CMakeFiles/Pythia_one_inclusive.dir/src/bin_generator.cpp.i
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_one_jet_inclusive.dir/build.make CMakeFiles/Pythia_one_jet_inclusive.dir/src/bin_generator.cpp.i
.PHONY : src/bin_generator.cpp.i

src/bin_generator.s: src/bin_generator.cpp.s
.PHONY : src/bin_generator.s

# target to generate assembly for a file
src/bin_generator.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/EC_resummation.dir/build.make CMakeFiles/EC_resummation.dir/src/bin_generator.cpp.s
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cross.dir/build.make CMakeFiles/cross.dir/src/bin_generator.cpp.s
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_EC_FO.dir/build.make CMakeFiles/Pythia_EC_FO.dir/src/bin_generator.cpp.s
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_EC_resum.dir/build.make CMakeFiles/Pythia_EC_resum.dir/src/bin_generator.cpp.s
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_MG5_EEC_e-e+.dir/build.make CMakeFiles/Pythia_MG5_EEC_e-e+.dir/src/bin_generator.cpp.s
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_one_inclusive.dir/build.make CMakeFiles/Pythia_one_inclusive.dir/src/bin_generator.cpp.s
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_one_jet_inclusive.dir/build.make CMakeFiles/Pythia_one_jet_inclusive.dir/src/bin_generator.cpp.s
.PHONY : src/bin_generator.cpp.s

src/jet.o: src/jet.cpp.o
.PHONY : src/jet.o

# target to build an object file
src/jet.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/EC_resummation.dir/build.make CMakeFiles/EC_resummation.dir/src/jet.cpp.o
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cross.dir/build.make CMakeFiles/cross.dir/src/jet.cpp.o
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_EC_FO.dir/build.make CMakeFiles/Pythia_EC_FO.dir/src/jet.cpp.o
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_EC_resum.dir/build.make CMakeFiles/Pythia_EC_resum.dir/src/jet.cpp.o
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_MG5_EEC_e-e+.dir/build.make CMakeFiles/Pythia_MG5_EEC_e-e+.dir/src/jet.cpp.o
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_one_inclusive.dir/build.make CMakeFiles/Pythia_one_inclusive.dir/src/jet.cpp.o
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_one_jet_inclusive.dir/build.make CMakeFiles/Pythia_one_jet_inclusive.dir/src/jet.cpp.o
.PHONY : src/jet.cpp.o

src/jet.i: src/jet.cpp.i
.PHONY : src/jet.i

# target to preprocess a source file
src/jet.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/EC_resummation.dir/build.make CMakeFiles/EC_resummation.dir/src/jet.cpp.i
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cross.dir/build.make CMakeFiles/cross.dir/src/jet.cpp.i
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_EC_FO.dir/build.make CMakeFiles/Pythia_EC_FO.dir/src/jet.cpp.i
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_EC_resum.dir/build.make CMakeFiles/Pythia_EC_resum.dir/src/jet.cpp.i
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_MG5_EEC_e-e+.dir/build.make CMakeFiles/Pythia_MG5_EEC_e-e+.dir/src/jet.cpp.i
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_one_inclusive.dir/build.make CMakeFiles/Pythia_one_inclusive.dir/src/jet.cpp.i
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_one_jet_inclusive.dir/build.make CMakeFiles/Pythia_one_jet_inclusive.dir/src/jet.cpp.i
.PHONY : src/jet.cpp.i

src/jet.s: src/jet.cpp.s
.PHONY : src/jet.s

# target to generate assembly for a file
src/jet.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/EC_resummation.dir/build.make CMakeFiles/EC_resummation.dir/src/jet.cpp.s
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cross.dir/build.make CMakeFiles/cross.dir/src/jet.cpp.s
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_EC_FO.dir/build.make CMakeFiles/Pythia_EC_FO.dir/src/jet.cpp.s
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_EC_resum.dir/build.make CMakeFiles/Pythia_EC_resum.dir/src/jet.cpp.s
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_MG5_EEC_e-e+.dir/build.make CMakeFiles/Pythia_MG5_EEC_e-e+.dir/src/jet.cpp.s
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_one_inclusive.dir/build.make CMakeFiles/Pythia_one_inclusive.dir/src/jet.cpp.s
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_one_jet_inclusive.dir/build.make CMakeFiles/Pythia_one_jet_inclusive.dir/src/jet.cpp.s
.PHONY : src/jet.cpp.s

src/jet_function.o: src/jet_function.cpp.o
.PHONY : src/jet_function.o

# target to build an object file
src/jet_function.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/EC_resummation.dir/build.make CMakeFiles/EC_resummation.dir/src/jet_function.cpp.o
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cross.dir/build.make CMakeFiles/cross.dir/src/jet_function.cpp.o
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_EC_FO.dir/build.make CMakeFiles/Pythia_EC_FO.dir/src/jet_function.cpp.o
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_EC_resum.dir/build.make CMakeFiles/Pythia_EC_resum.dir/src/jet_function.cpp.o
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_MG5_EEC_e-e+.dir/build.make CMakeFiles/Pythia_MG5_EEC_e-e+.dir/src/jet_function.cpp.o
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_one_inclusive.dir/build.make CMakeFiles/Pythia_one_inclusive.dir/src/jet_function.cpp.o
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_one_jet_inclusive.dir/build.make CMakeFiles/Pythia_one_jet_inclusive.dir/src/jet_function.cpp.o
.PHONY : src/jet_function.cpp.o

src/jet_function.i: src/jet_function.cpp.i
.PHONY : src/jet_function.i

# target to preprocess a source file
src/jet_function.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/EC_resummation.dir/build.make CMakeFiles/EC_resummation.dir/src/jet_function.cpp.i
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cross.dir/build.make CMakeFiles/cross.dir/src/jet_function.cpp.i
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_EC_FO.dir/build.make CMakeFiles/Pythia_EC_FO.dir/src/jet_function.cpp.i
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_EC_resum.dir/build.make CMakeFiles/Pythia_EC_resum.dir/src/jet_function.cpp.i
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_MG5_EEC_e-e+.dir/build.make CMakeFiles/Pythia_MG5_EEC_e-e+.dir/src/jet_function.cpp.i
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_one_inclusive.dir/build.make CMakeFiles/Pythia_one_inclusive.dir/src/jet_function.cpp.i
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_one_jet_inclusive.dir/build.make CMakeFiles/Pythia_one_jet_inclusive.dir/src/jet_function.cpp.i
.PHONY : src/jet_function.cpp.i

src/jet_function.s: src/jet_function.cpp.s
.PHONY : src/jet_function.s

# target to generate assembly for a file
src/jet_function.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/EC_resummation.dir/build.make CMakeFiles/EC_resummation.dir/src/jet_function.cpp.s
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cross.dir/build.make CMakeFiles/cross.dir/src/jet_function.cpp.s
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_EC_FO.dir/build.make CMakeFiles/Pythia_EC_FO.dir/src/jet_function.cpp.s
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_EC_resum.dir/build.make CMakeFiles/Pythia_EC_resum.dir/src/jet_function.cpp.s
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_MG5_EEC_e-e+.dir/build.make CMakeFiles/Pythia_MG5_EEC_e-e+.dir/src/jet_function.cpp.s
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_one_inclusive.dir/build.make CMakeFiles/Pythia_one_inclusive.dir/src/jet_function.cpp.s
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_one_jet_inclusive.dir/build.make CMakeFiles/Pythia_one_jet_inclusive.dir/src/jet_function.cpp.s
.PHONY : src/jet_function.cpp.s

src/phase_space.o: src/phase_space.cpp.o
.PHONY : src/phase_space.o

# target to build an object file
src/phase_space.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/EC_resummation.dir/build.make CMakeFiles/EC_resummation.dir/src/phase_space.cpp.o
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cross.dir/build.make CMakeFiles/cross.dir/src/phase_space.cpp.o
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_EC_FO.dir/build.make CMakeFiles/Pythia_EC_FO.dir/src/phase_space.cpp.o
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_EC_resum.dir/build.make CMakeFiles/Pythia_EC_resum.dir/src/phase_space.cpp.o
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_MG5_EEC_e-e+.dir/build.make CMakeFiles/Pythia_MG5_EEC_e-e+.dir/src/phase_space.cpp.o
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_one_inclusive.dir/build.make CMakeFiles/Pythia_one_inclusive.dir/src/phase_space.cpp.o
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_one_jet_inclusive.dir/build.make CMakeFiles/Pythia_one_jet_inclusive.dir/src/phase_space.cpp.o
.PHONY : src/phase_space.cpp.o

src/phase_space.i: src/phase_space.cpp.i
.PHONY : src/phase_space.i

# target to preprocess a source file
src/phase_space.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/EC_resummation.dir/build.make CMakeFiles/EC_resummation.dir/src/phase_space.cpp.i
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cross.dir/build.make CMakeFiles/cross.dir/src/phase_space.cpp.i
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_EC_FO.dir/build.make CMakeFiles/Pythia_EC_FO.dir/src/phase_space.cpp.i
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_EC_resum.dir/build.make CMakeFiles/Pythia_EC_resum.dir/src/phase_space.cpp.i
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_MG5_EEC_e-e+.dir/build.make CMakeFiles/Pythia_MG5_EEC_e-e+.dir/src/phase_space.cpp.i
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_one_inclusive.dir/build.make CMakeFiles/Pythia_one_inclusive.dir/src/phase_space.cpp.i
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_one_jet_inclusive.dir/build.make CMakeFiles/Pythia_one_jet_inclusive.dir/src/phase_space.cpp.i
.PHONY : src/phase_space.cpp.i

src/phase_space.s: src/phase_space.cpp.s
.PHONY : src/phase_space.s

# target to generate assembly for a file
src/phase_space.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/EC_resummation.dir/build.make CMakeFiles/EC_resummation.dir/src/phase_space.cpp.s
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cross.dir/build.make CMakeFiles/cross.dir/src/phase_space.cpp.s
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_EC_FO.dir/build.make CMakeFiles/Pythia_EC_FO.dir/src/phase_space.cpp.s
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_EC_resum.dir/build.make CMakeFiles/Pythia_EC_resum.dir/src/phase_space.cpp.s
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_MG5_EEC_e-e+.dir/build.make CMakeFiles/Pythia_MG5_EEC_e-e+.dir/src/phase_space.cpp.s
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_one_inclusive.dir/build.make CMakeFiles/Pythia_one_inclusive.dir/src/phase_space.cpp.s
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_one_jet_inclusive.dir/build.make CMakeFiles/Pythia_one_jet_inclusive.dir/src/phase_space.cpp.s
.PHONY : src/phase_space.cpp.s

src/process_data.o: src/process_data.cpp.o
.PHONY : src/process_data.o

# target to build an object file
src/process_data.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/EC_resummation.dir/build.make CMakeFiles/EC_resummation.dir/src/process_data.cpp.o
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cross.dir/build.make CMakeFiles/cross.dir/src/process_data.cpp.o
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_EC_FO.dir/build.make CMakeFiles/Pythia_EC_FO.dir/src/process_data.cpp.o
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_EC_resum.dir/build.make CMakeFiles/Pythia_EC_resum.dir/src/process_data.cpp.o
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_MG5_EEC_e-e+.dir/build.make CMakeFiles/Pythia_MG5_EEC_e-e+.dir/src/process_data.cpp.o
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_one_inclusive.dir/build.make CMakeFiles/Pythia_one_inclusive.dir/src/process_data.cpp.o
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_one_jet_inclusive.dir/build.make CMakeFiles/Pythia_one_jet_inclusive.dir/src/process_data.cpp.o
.PHONY : src/process_data.cpp.o

src/process_data.i: src/process_data.cpp.i
.PHONY : src/process_data.i

# target to preprocess a source file
src/process_data.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/EC_resummation.dir/build.make CMakeFiles/EC_resummation.dir/src/process_data.cpp.i
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cross.dir/build.make CMakeFiles/cross.dir/src/process_data.cpp.i
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_EC_FO.dir/build.make CMakeFiles/Pythia_EC_FO.dir/src/process_data.cpp.i
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_EC_resum.dir/build.make CMakeFiles/Pythia_EC_resum.dir/src/process_data.cpp.i
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_MG5_EEC_e-e+.dir/build.make CMakeFiles/Pythia_MG5_EEC_e-e+.dir/src/process_data.cpp.i
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_one_inclusive.dir/build.make CMakeFiles/Pythia_one_inclusive.dir/src/process_data.cpp.i
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_one_jet_inclusive.dir/build.make CMakeFiles/Pythia_one_jet_inclusive.dir/src/process_data.cpp.i
.PHONY : src/process_data.cpp.i

src/process_data.s: src/process_data.cpp.s
.PHONY : src/process_data.s

# target to generate assembly for a file
src/process_data.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/EC_resummation.dir/build.make CMakeFiles/EC_resummation.dir/src/process_data.cpp.s
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cross.dir/build.make CMakeFiles/cross.dir/src/process_data.cpp.s
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_EC_FO.dir/build.make CMakeFiles/Pythia_EC_FO.dir/src/process_data.cpp.s
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_EC_resum.dir/build.make CMakeFiles/Pythia_EC_resum.dir/src/process_data.cpp.s
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_MG5_EEC_e-e+.dir/build.make CMakeFiles/Pythia_MG5_EEC_e-e+.dir/src/process_data.cpp.s
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_one_inclusive.dir/build.make CMakeFiles/Pythia_one_inclusive.dir/src/process_data.cpp.s
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_one_jet_inclusive.dir/build.make CMakeFiles/Pythia_one_jet_inclusive.dir/src/process_data.cpp.s
.PHONY : src/process_data.cpp.s

src/run_parameters.o: src/run_parameters.cpp.o
.PHONY : src/run_parameters.o

# target to build an object file
src/run_parameters.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/EC_resummation.dir/build.make CMakeFiles/EC_resummation.dir/src/run_parameters.cpp.o
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cross.dir/build.make CMakeFiles/cross.dir/src/run_parameters.cpp.o
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_EC_FO.dir/build.make CMakeFiles/Pythia_EC_FO.dir/src/run_parameters.cpp.o
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_EC_resum.dir/build.make CMakeFiles/Pythia_EC_resum.dir/src/run_parameters.cpp.o
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_MG5_EEC_e-e+.dir/build.make CMakeFiles/Pythia_MG5_EEC_e-e+.dir/src/run_parameters.cpp.o
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_one_inclusive.dir/build.make CMakeFiles/Pythia_one_inclusive.dir/src/run_parameters.cpp.o
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_one_jet_inclusive.dir/build.make CMakeFiles/Pythia_one_jet_inclusive.dir/src/run_parameters.cpp.o
.PHONY : src/run_parameters.cpp.o

src/run_parameters.i: src/run_parameters.cpp.i
.PHONY : src/run_parameters.i

# target to preprocess a source file
src/run_parameters.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/EC_resummation.dir/build.make CMakeFiles/EC_resummation.dir/src/run_parameters.cpp.i
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cross.dir/build.make CMakeFiles/cross.dir/src/run_parameters.cpp.i
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_EC_FO.dir/build.make CMakeFiles/Pythia_EC_FO.dir/src/run_parameters.cpp.i
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_EC_resum.dir/build.make CMakeFiles/Pythia_EC_resum.dir/src/run_parameters.cpp.i
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_MG5_EEC_e-e+.dir/build.make CMakeFiles/Pythia_MG5_EEC_e-e+.dir/src/run_parameters.cpp.i
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_one_inclusive.dir/build.make CMakeFiles/Pythia_one_inclusive.dir/src/run_parameters.cpp.i
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_one_jet_inclusive.dir/build.make CMakeFiles/Pythia_one_jet_inclusive.dir/src/run_parameters.cpp.i
.PHONY : src/run_parameters.cpp.i

src/run_parameters.s: src/run_parameters.cpp.s
.PHONY : src/run_parameters.s

# target to generate assembly for a file
src/run_parameters.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/EC_resummation.dir/build.make CMakeFiles/EC_resummation.dir/src/run_parameters.cpp.s
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cross.dir/build.make CMakeFiles/cross.dir/src/run_parameters.cpp.s
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_EC_FO.dir/build.make CMakeFiles/Pythia_EC_FO.dir/src/run_parameters.cpp.s
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_EC_resum.dir/build.make CMakeFiles/Pythia_EC_resum.dir/src/run_parameters.cpp.s
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_MG5_EEC_e-e+.dir/build.make CMakeFiles/Pythia_MG5_EEC_e-e+.dir/src/run_parameters.cpp.s
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_one_inclusive.dir/build.make CMakeFiles/Pythia_one_inclusive.dir/src/run_parameters.cpp.s
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_one_jet_inclusive.dir/build.make CMakeFiles/Pythia_one_jet_inclusive.dir/src/run_parameters.cpp.s
.PHONY : src/run_parameters.cpp.s

src/vegas_parameters.o: src/vegas_parameters.cpp.o
.PHONY : src/vegas_parameters.o

# target to build an object file
src/vegas_parameters.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/EC_resummation.dir/build.make CMakeFiles/EC_resummation.dir/src/vegas_parameters.cpp.o
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cross.dir/build.make CMakeFiles/cross.dir/src/vegas_parameters.cpp.o
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_EC_FO.dir/build.make CMakeFiles/Pythia_EC_FO.dir/src/vegas_parameters.cpp.o
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_EC_resum.dir/build.make CMakeFiles/Pythia_EC_resum.dir/src/vegas_parameters.cpp.o
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_MG5_EEC_e-e+.dir/build.make CMakeFiles/Pythia_MG5_EEC_e-e+.dir/src/vegas_parameters.cpp.o
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_one_inclusive.dir/build.make CMakeFiles/Pythia_one_inclusive.dir/src/vegas_parameters.cpp.o
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_one_jet_inclusive.dir/build.make CMakeFiles/Pythia_one_jet_inclusive.dir/src/vegas_parameters.cpp.o
.PHONY : src/vegas_parameters.cpp.o

src/vegas_parameters.i: src/vegas_parameters.cpp.i
.PHONY : src/vegas_parameters.i

# target to preprocess a source file
src/vegas_parameters.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/EC_resummation.dir/build.make CMakeFiles/EC_resummation.dir/src/vegas_parameters.cpp.i
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cross.dir/build.make CMakeFiles/cross.dir/src/vegas_parameters.cpp.i
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_EC_FO.dir/build.make CMakeFiles/Pythia_EC_FO.dir/src/vegas_parameters.cpp.i
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_EC_resum.dir/build.make CMakeFiles/Pythia_EC_resum.dir/src/vegas_parameters.cpp.i
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_MG5_EEC_e-e+.dir/build.make CMakeFiles/Pythia_MG5_EEC_e-e+.dir/src/vegas_parameters.cpp.i
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_one_inclusive.dir/build.make CMakeFiles/Pythia_one_inclusive.dir/src/vegas_parameters.cpp.i
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_one_jet_inclusive.dir/build.make CMakeFiles/Pythia_one_jet_inclusive.dir/src/vegas_parameters.cpp.i
.PHONY : src/vegas_parameters.cpp.i

src/vegas_parameters.s: src/vegas_parameters.cpp.s
.PHONY : src/vegas_parameters.s

# target to generate assembly for a file
src/vegas_parameters.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/EC_resummation.dir/build.make CMakeFiles/EC_resummation.dir/src/vegas_parameters.cpp.s
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cross.dir/build.make CMakeFiles/cross.dir/src/vegas_parameters.cpp.s
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_EC_FO.dir/build.make CMakeFiles/Pythia_EC_FO.dir/src/vegas_parameters.cpp.s
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_EC_resum.dir/build.make CMakeFiles/Pythia_EC_resum.dir/src/vegas_parameters.cpp.s
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_MG5_EEC_e-e+.dir/build.make CMakeFiles/Pythia_MG5_EEC_e-e+.dir/src/vegas_parameters.cpp.s
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_one_inclusive.dir/build.make CMakeFiles/Pythia_one_inclusive.dir/src/vegas_parameters.cpp.s
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_one_jet_inclusive.dir/build.make CMakeFiles/Pythia_one_jet_inclusive.dir/src/vegas_parameters.cpp.s
.PHONY : src/vegas_parameters.cpp.s

target/EC/EC_resummation.o: target/EC/EC_resummation.cpp.o
.PHONY : target/EC/EC_resummation.o

# target to build an object file
target/EC/EC_resummation.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/EC_resummation.dir/build.make CMakeFiles/EC_resummation.dir/target/EC/EC_resummation.cpp.o
.PHONY : target/EC/EC_resummation.cpp.o

target/EC/EC_resummation.i: target/EC/EC_resummation.cpp.i
.PHONY : target/EC/EC_resummation.i

# target to preprocess a source file
target/EC/EC_resummation.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/EC_resummation.dir/build.make CMakeFiles/EC_resummation.dir/target/EC/EC_resummation.cpp.i
.PHONY : target/EC/EC_resummation.cpp.i

target/EC/EC_resummation.s: target/EC/EC_resummation.cpp.s
.PHONY : target/EC/EC_resummation.s

# target to generate assembly for a file
target/EC/EC_resummation.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/EC_resummation.dir/build.make CMakeFiles/EC_resummation.dir/target/EC/EC_resummation.cpp.s
.PHONY : target/EC/EC_resummation.cpp.s

target/EC/cross.o: target/EC/cross.cpp.o
.PHONY : target/EC/cross.o

# target to build an object file
target/EC/cross.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cross.dir/build.make CMakeFiles/cross.dir/target/EC/cross.cpp.o
.PHONY : target/EC/cross.cpp.o

target/EC/cross.i: target/EC/cross.cpp.i
.PHONY : target/EC/cross.i

# target to preprocess a source file
target/EC/cross.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cross.dir/build.make CMakeFiles/cross.dir/target/EC/cross.cpp.i
.PHONY : target/EC/cross.cpp.i

target/EC/cross.s: target/EC/cross.cpp.s
.PHONY : target/EC/cross.s

# target to generate assembly for a file
target/EC/cross.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cross.dir/build.make CMakeFiles/cross.dir/target/EC/cross.cpp.s
.PHONY : target/EC/cross.cpp.s

target/pythia/Pythia_EC_FO.o: target/pythia/Pythia_EC_FO.cpp.o
.PHONY : target/pythia/Pythia_EC_FO.o

# target to build an object file
target/pythia/Pythia_EC_FO.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_EC_FO.dir/build.make CMakeFiles/Pythia_EC_FO.dir/target/pythia/Pythia_EC_FO.cpp.o
.PHONY : target/pythia/Pythia_EC_FO.cpp.o

target/pythia/Pythia_EC_FO.i: target/pythia/Pythia_EC_FO.cpp.i
.PHONY : target/pythia/Pythia_EC_FO.i

# target to preprocess a source file
target/pythia/Pythia_EC_FO.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_EC_FO.dir/build.make CMakeFiles/Pythia_EC_FO.dir/target/pythia/Pythia_EC_FO.cpp.i
.PHONY : target/pythia/Pythia_EC_FO.cpp.i

target/pythia/Pythia_EC_FO.s: target/pythia/Pythia_EC_FO.cpp.s
.PHONY : target/pythia/Pythia_EC_FO.s

# target to generate assembly for a file
target/pythia/Pythia_EC_FO.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_EC_FO.dir/build.make CMakeFiles/Pythia_EC_FO.dir/target/pythia/Pythia_EC_FO.cpp.s
.PHONY : target/pythia/Pythia_EC_FO.cpp.s

target/pythia/Pythia_EC_resum.o: target/pythia/Pythia_EC_resum.cpp.o
.PHONY : target/pythia/Pythia_EC_resum.o

# target to build an object file
target/pythia/Pythia_EC_resum.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_EC_resum.dir/build.make CMakeFiles/Pythia_EC_resum.dir/target/pythia/Pythia_EC_resum.cpp.o
.PHONY : target/pythia/Pythia_EC_resum.cpp.o

target/pythia/Pythia_EC_resum.i: target/pythia/Pythia_EC_resum.cpp.i
.PHONY : target/pythia/Pythia_EC_resum.i

# target to preprocess a source file
target/pythia/Pythia_EC_resum.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_EC_resum.dir/build.make CMakeFiles/Pythia_EC_resum.dir/target/pythia/Pythia_EC_resum.cpp.i
.PHONY : target/pythia/Pythia_EC_resum.cpp.i

target/pythia/Pythia_EC_resum.s: target/pythia/Pythia_EC_resum.cpp.s
.PHONY : target/pythia/Pythia_EC_resum.s

# target to generate assembly for a file
target/pythia/Pythia_EC_resum.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_EC_resum.dir/build.make CMakeFiles/Pythia_EC_resum.dir/target/pythia/Pythia_EC_resum.cpp.s
.PHONY : target/pythia/Pythia_EC_resum.cpp.s

target/pythia/Pythia_MG5_EEC_e-e+.o: target/pythia/Pythia_MG5_EEC_e-e+.cpp.o
.PHONY : target/pythia/Pythia_MG5_EEC_e-e+.o

# target to build an object file
target/pythia/Pythia_MG5_EEC_e-e+.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_MG5_EEC_e-e+.dir/build.make CMakeFiles/Pythia_MG5_EEC_e-e+.dir/target/pythia/Pythia_MG5_EEC_e-e+.cpp.o
.PHONY : target/pythia/Pythia_MG5_EEC_e-e+.cpp.o

target/pythia/Pythia_MG5_EEC_e-e+.i: target/pythia/Pythia_MG5_EEC_e-e+.cpp.i
.PHONY : target/pythia/Pythia_MG5_EEC_e-e+.i

# target to preprocess a source file
target/pythia/Pythia_MG5_EEC_e-e+.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_MG5_EEC_e-e+.dir/build.make CMakeFiles/Pythia_MG5_EEC_e-e+.dir/target/pythia/Pythia_MG5_EEC_e-e+.cpp.i
.PHONY : target/pythia/Pythia_MG5_EEC_e-e+.cpp.i

target/pythia/Pythia_MG5_EEC_e-e+.s: target/pythia/Pythia_MG5_EEC_e-e+.cpp.s
.PHONY : target/pythia/Pythia_MG5_EEC_e-e+.s

# target to generate assembly for a file
target/pythia/Pythia_MG5_EEC_e-e+.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_MG5_EEC_e-e+.dir/build.make CMakeFiles/Pythia_MG5_EEC_e-e+.dir/target/pythia/Pythia_MG5_EEC_e-e+.cpp.s
.PHONY : target/pythia/Pythia_MG5_EEC_e-e+.cpp.s

target/pythia/Pythia_one_inclusive.o: target/pythia/Pythia_one_inclusive.cpp.o
.PHONY : target/pythia/Pythia_one_inclusive.o

# target to build an object file
target/pythia/Pythia_one_inclusive.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_one_inclusive.dir/build.make CMakeFiles/Pythia_one_inclusive.dir/target/pythia/Pythia_one_inclusive.cpp.o
.PHONY : target/pythia/Pythia_one_inclusive.cpp.o

target/pythia/Pythia_one_inclusive.i: target/pythia/Pythia_one_inclusive.cpp.i
.PHONY : target/pythia/Pythia_one_inclusive.i

# target to preprocess a source file
target/pythia/Pythia_one_inclusive.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_one_inclusive.dir/build.make CMakeFiles/Pythia_one_inclusive.dir/target/pythia/Pythia_one_inclusive.cpp.i
.PHONY : target/pythia/Pythia_one_inclusive.cpp.i

target/pythia/Pythia_one_inclusive.s: target/pythia/Pythia_one_inclusive.cpp.s
.PHONY : target/pythia/Pythia_one_inclusive.s

# target to generate assembly for a file
target/pythia/Pythia_one_inclusive.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_one_inclusive.dir/build.make CMakeFiles/Pythia_one_inclusive.dir/target/pythia/Pythia_one_inclusive.cpp.s
.PHONY : target/pythia/Pythia_one_inclusive.cpp.s

target/pythia/Pythia_one_jet_inclusive.o: target/pythia/Pythia_one_jet_inclusive.cpp.o
.PHONY : target/pythia/Pythia_one_jet_inclusive.o

# target to build an object file
target/pythia/Pythia_one_jet_inclusive.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_one_jet_inclusive.dir/build.make CMakeFiles/Pythia_one_jet_inclusive.dir/target/pythia/Pythia_one_jet_inclusive.cpp.o
.PHONY : target/pythia/Pythia_one_jet_inclusive.cpp.o

target/pythia/Pythia_one_jet_inclusive.i: target/pythia/Pythia_one_jet_inclusive.cpp.i
.PHONY : target/pythia/Pythia_one_jet_inclusive.i

# target to preprocess a source file
target/pythia/Pythia_one_jet_inclusive.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_one_jet_inclusive.dir/build.make CMakeFiles/Pythia_one_jet_inclusive.dir/target/pythia/Pythia_one_jet_inclusive.cpp.i
.PHONY : target/pythia/Pythia_one_jet_inclusive.cpp.i

target/pythia/Pythia_one_jet_inclusive.s: target/pythia/Pythia_one_jet_inclusive.cpp.s
.PHONY : target/pythia/Pythia_one_jet_inclusive.s

# target to generate assembly for a file
target/pythia/Pythia_one_jet_inclusive.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Pythia_one_jet_inclusive.dir/build.make CMakeFiles/Pythia_one_jet_inclusive.dir/target/pythia/Pythia_one_jet_inclusive.cpp.s
.PHONY : target/pythia/Pythia_one_jet_inclusive.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... rebuild_cache"
	@echo "... EC_resummation"
	@echo "... Pythia_EC_FO"
	@echo "... Pythia_EC_resum"
	@echo "... Pythia_MG5_EEC_e-e+"
	@echo "... Pythia_one_inclusive"
	@echo "... Pythia_one_jet_inclusive"
	@echo "... cross"
	@echo "... src/EC.o"
	@echo "... src/EC.i"
	@echo "... src/EC.s"
	@echo "... src/amplitude.o"
	@echo "... src/amplitude.i"
	@echo "... src/amplitude.s"
	@echo "... src/basic_function.o"
	@echo "... src/basic_function.i"
	@echo "... src/basic_function.s"
	@echo "... src/bin_generator.o"
	@echo "... src/bin_generator.i"
	@echo "... src/bin_generator.s"
	@echo "... src/jet.o"
	@echo "... src/jet.i"
	@echo "... src/jet.s"
	@echo "... src/jet_function.o"
	@echo "... src/jet_function.i"
	@echo "... src/jet_function.s"
	@echo "... src/phase_space.o"
	@echo "... src/phase_space.i"
	@echo "... src/phase_space.s"
	@echo "... src/process_data.o"
	@echo "... src/process_data.i"
	@echo "... src/process_data.s"
	@echo "... src/run_parameters.o"
	@echo "... src/run_parameters.i"
	@echo "... src/run_parameters.s"
	@echo "... src/vegas_parameters.o"
	@echo "... src/vegas_parameters.i"
	@echo "... src/vegas_parameters.s"
	@echo "... target/EC/EC_resummation.o"
	@echo "... target/EC/EC_resummation.i"
	@echo "... target/EC/EC_resummation.s"
	@echo "... target/EC/cross.o"
	@echo "... target/EC/cross.i"
	@echo "... target/EC/cross.s"
	@echo "... target/pythia/Pythia_EC_FO.o"
	@echo "... target/pythia/Pythia_EC_FO.i"
	@echo "... target/pythia/Pythia_EC_FO.s"
	@echo "... target/pythia/Pythia_EC_resum.o"
	@echo "... target/pythia/Pythia_EC_resum.i"
	@echo "... target/pythia/Pythia_EC_resum.s"
	@echo "... target/pythia/Pythia_MG5_EEC_e-e+.o"
	@echo "... target/pythia/Pythia_MG5_EEC_e-e+.i"
	@echo "... target/pythia/Pythia_MG5_EEC_e-e+.s"
	@echo "... target/pythia/Pythia_one_inclusive.o"
	@echo "... target/pythia/Pythia_one_inclusive.i"
	@echo "... target/pythia/Pythia_one_inclusive.s"
	@echo "... target/pythia/Pythia_one_jet_inclusive.o"
	@echo "... target/pythia/Pythia_one_jet_inclusive.i"
	@echo "... target/pythia/Pythia_one_jet_inclusive.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

