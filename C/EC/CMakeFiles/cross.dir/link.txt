/usr/bin/c++ CMakeFiles/cross.dir/target/EC/cross.cpp.o CMakeFiles/cross.dir/src/EC.cpp.o CMakeFiles/cross.dir/src/amplitude.cpp.o CMakeFiles/cross.dir/src/basic_function.cpp.o CMakeFiles/cross.dir/src/bin_generator.cpp.o CMakeFiles/cross.dir/src/jet.cpp.o CMakeFiles/cross.dir/src/jet_function.cpp.o CMakeFiles/cross.dir/src/phase_space.cpp.o CMakeFiles/cross.dir/src/process_data.cpp.o CMakeFiles/cross.dir/src/run_parameters.cpp.o CMakeFiles/cross.dir/src/vegas_parameters.cpp.o -o build/bin/cross   -L/home/<USER>/Project/HEPLib/GSL/lib  -L/home/<USER>/Project/HEPLib/Cuba/lib  -L/home/<USER>/Project/HEPLib/Pythia/lib  -L/home/<USER>/Project/HEPLib/LHAPDF/lib  -L/home/<USER>/Project/HEPLib/FastJet/lib  -Wl,-rpath,/home/<USER>/Project/HEPLib/GSL/lib:/home/<USER>/Project/HEPLib/Cuba/lib:/home/<USER>/Project/HEPLib/Pythia/lib:/home/<USER>/Project/HEPLib/LHAPDF/lib:/home/<USER>/Project/HEPLib/FastJet/lib -lgsl -lcuba -lLHAPDF -lgfortran -lgslcblas 
