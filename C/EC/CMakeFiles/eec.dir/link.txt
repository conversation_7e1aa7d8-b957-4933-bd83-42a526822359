/usr/bin/c++ CMakeFiles/eec.dir/target/pythia/eec.cpp.o CMakeFiles/eec.dir/src/EC.cpp.o CMakeFiles/eec.dir/src/amplitude.cpp.o CMakeFiles/eec.dir/src/basic_function.cpp.o CMakeFiles/eec.dir/src/bin_generator.cpp.o CMakeFiles/eec.dir/src/jet.cpp.o CMakeFiles/eec.dir/src/jet_function.cpp.o CMakeFiles/eec.dir/src/phase_space.cpp.o CMakeFiles/eec.dir/src/process_data.cpp.o CMakeFiles/eec.dir/src/run_parameters.cpp.o CMakeFiles/eec.dir/src/vegas_parameters.cpp.o -o build/bin/eec   -L/home/<USER>/Project/HEPLib/GSL/lib  -L/home/<USER>/Project/HEPLib/Cuba/lib  -L/home/<USER>/Project/HEPLib/Pythia/lib  -L/home/<USER>/Project/HEPLib/LHAPDF/lib  -L/home/<USER>/Project/HEPLib/FastJet/lib  -Wl,-rpath,/home/<USER>/Project/HEPLib/GSL/lib:/home/<USER>/Project/HEPLib/Cuba/lib:/home/<USER>/Project/HEPLib/Pythia/lib:/home/<USER>/Project/HEPLib/LHAPDF/lib:/home/<USER>/Project/HEPLib/FastJet/lib -lpythia8 -lfastjet -lz -lgsl -lcuba -lLHAPDF -lgfortran -lgslcblas 
