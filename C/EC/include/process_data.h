#ifndef PROCESS_DATA_H
#define PROCESS_DATA_H

#include <string>

struct ProcessParameters {
    std::string accuracy;
    std::string scale_variation;
    double Q_beam;
    double pT_min;
    double pT_max;
    double eta_min;
    double eta_max;
    double c;
    double R;
    int num_bin1;
    int num_bin2;
    double min_val;
    double med_val;
    double max_val;
};

bool process_data(const std::string &input_filename,
                  const std::string &output_dir);

#endif // PROCESS_DATA_H
