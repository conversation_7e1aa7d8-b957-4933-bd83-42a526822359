#ifndef PHASE_SPACE_H
#define PHASE_SPACE_H

#include <cmath>

namespace phase_space_pp {
extern double Q_beam;
extern double pT_min;
extern double pT_max;
extern double eta_min;
extern double eta_max;

double S();
double T(double pt, double eta);
double U(double pt, double eta);
double V(double pt, double eta);
double W(double pt, double eta);
double x1(double v, double w, double zc, double pt, double eta);
double x2(double v, double w, double zc, double pt, double eta);
double shat(double v, double w, double zc, double pt, double eta);
}  // namespace phase_space_pp

namespace phase_space_epa {
extern double Q_beam;
extern double pT_min;
extern double pT_max;
extern double eta_min;
extern double eta_max;

double S();
double pthat(double pt, double zc);
double v(double pt, double eta, double zc);
double z(double pt, double eta, double zc);

// double v__(double pt, double eta, double zc);
// double w(double pt, double eta, double zc);
// double s();
// double t(double pt, double eta, double zc);
// double u(double pt, double eta, double zc);
}  // namespace phase_space_epa

#endif  // PHASE_SPACE_H
