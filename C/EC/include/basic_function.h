#ifndef BASIC_FUNCTION_H
#define BASIC_FUNCTION_H

#include "LHAPDF/LHAPDF.h"

#include <vector>

using namespace std;

extern LHAPDF::PDF* pdf;
void                init_pdf(int set_id);
void                clear_pdf();

extern const double CF;
extern const double CA;
extern const double NF;
extern const double TF;
extern const double NC;

extern const double bmax;
extern const double Q0;
extern const double g2;
extern const double gh;

extern double mu_b(double b);
extern double bstar(double b);
extern double L(double wJ, double u, double R);

extern const double aem;
extern double       ash(double u);

extern vector<double> Beta;
extern vector<double> Icusp_q;
extern vector<double> Icusp_g;
extern vector<double> gamma_d_q;
extern vector<double> gamma_d_g;
extern vector<double> gamma_s_q;
extern vector<double> gamma_s_g;
extern vector<double> gamma_r_q;
extern vector<double> gamma_r_g;

extern vector<double> Pqq(double z);
extern vector<double> Pgq(double z);
extern vector<double> Pqg(double z);
extern vector<double> Pgg(double z);

extern double gamma_r(double pid, double order, double u);

extern vector<double> hard(int pid1, int pid2, int order, double as, double z, double wJ, double R, double u);

#endif  // BASIC_FUNCTION_H
