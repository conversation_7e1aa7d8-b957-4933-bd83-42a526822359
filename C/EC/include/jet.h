#ifndef JET_H_
#define JET_H_

#include <vector>

namespace jet_XS_epa {

double jet_XS(int order, std::vector<double> scale_variation, double R);

}  // namespace jet_XS_epa

namespace jet_XS_pp {
std::vector<double> dsigma_jet(double omegaJ, double v, double w, double zc, double pT, double eta, double R, double mu, const std::vector<double>& jetfun_q, const std::vector<double>& jetfun_g);

double jet_XS(int order, std::vector<double> scale_variation, double R);
}  // namespace jet_XS_pp

#endif  // JET_H_
