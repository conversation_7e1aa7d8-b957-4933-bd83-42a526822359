#ifndef JET_FUNCTION_H
#define JET_FUNCTION_H

#include <vector>

using namespace std;
// extern double sudakov_pert(int pid, int order, double u0, double u);

extern double         jetfun_FO(int pid, double theta, double omegaJ, double R, double mu);
extern vector<double> jetfun(int pid, int order, double zc, double omegaJ, double R, double mu);
extern vector<double> jetfun_resum_woDGLAP(int pid, vector<int> accuracy, double zc, double theta, double c, double omegaJ, double R, double b, double mub, double muJ, double mu);
extern vector<double> jetfun_resum_woDGLAP_woNP(int pid, vector<int> accuracy, double zc, double theta, double omegaJ, double R, double b, double mub, double muJ, double mu);

double sudakov_pert(int pid, int order, double mu0, double mu);
#endif  // JET_FUNCTION_H
