#ifndef RUN_PARAMETERS_H
#define RUN_PARAMETERS_H

#include <stdexcept>
#include <string>
#include <unordered_map>
#include <vector>

struct run_parameters
{
    std::vector<int> accuracy;
    std::vector<double> scale_variation;

    run_parameters(const std::string &accuracy_key, const std::string &scale_variation_key);

  private:
    static const std::vector<int> NLO;
    static const std::vector<int> LL;
    static const std::vector<int> NLL;
    static const std::vector<int> NLLPrime;
    static const std::vector<int> NNLL;

    static const std::vector<double> scheme1;
    static const std::vector<double> scheme2;
    static const std::vector<double> scheme3;

    static const std::unordered_map<std::string, const std::vector<int> *> accuracy_map;
    static const std::unordered_map<std::string, const std::vector<double> *> scale_variation_map;
};

#endif // RUN_PARAMETERS
