#ifndef EC_H
#define EC_H

#include "amplitude.h"
#include "cuba.h"
#include "jet_function.h"
#include "phase_space.h"
#include "vegas_parameters.h"

#include <vector>

namespace EC_epa {

double EC_NLO(std::vector<double> scale_variation, double theta, double R, vector<double> sigma);
double EC_resum_woDGLAP(vector<int> accuracy, vector<double> sclae_variation, double theta, double c, double R, vector<double> sigma);
double EC_resum_woDGLAP_woNP(vector<int> accuracy, vector<double> sclae_variation, double theta, double R, vector<double> sigma);

}  // namespace EC_epa

namespace EC_pp {

double EC_resum_woDGLAP(vector<int> accuracy, vector<double> sclae_variation, double theta, double c, double R, vector<double> sigma);
double EC_resum_woDGLAP_woNP(vector<int> accuracy, vector<double> sclae_variation, double theta, double R, vector<double> sigma);

}  // namespace EC_pp

#endif  // EC_H
