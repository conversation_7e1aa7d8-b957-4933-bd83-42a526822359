#ifndef BIN_GENERATOR_H
#define BIN_GENERATOR_H

#include <cmath>
#include <vector>

size_t find_bin_index(const std::vector<double>& bins, double value);

void log_bins(int num_bin1, int num_bin2, std::vector<double>& bin_edges, std::vector<double>& bin_log_midpoints, double min_val = 1e-5, double med_val = 1e-3, double max_val = 1e0);

void uniform_bins(int num_bins, std::vector<double>& bin_edges, std::vector<double>& bin_midpoints, double min_val, double max_val);

void generate_bins(int bin_type, int num_bin1, int num_bin2, std::vector<double>& bin_edges, std::vector<double>& bin_midpoints, double min_val, double med_val, double max_val);

#endif  // BIN_GENERATOR_H
