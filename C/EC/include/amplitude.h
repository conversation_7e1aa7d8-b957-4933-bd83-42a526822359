#ifndef AMPLITUDE_H
#define AMPLITUDE_H

namespace epa {

double dsigma(double zc, double pt, double eta, double u);

double XS_check_LO();

double XS_check_NLO();

}  // namespace epa

namespace pp {

double dsigma_dvdw(int chanel, double mu, double v, double w, double zc, double pT, double eta);

double pdf_prd(int chanel, double mu, double v, double w, double zc, double pT, double eta);

double dsigma(int chanel, double mu, double v, double w, double zc, double pT, double eta);

double XS_check_LO();

}  // namespace pp

#endif  // CROSS_SECTION_H
