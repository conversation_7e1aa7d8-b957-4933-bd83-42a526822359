from Notebook.Pre.Python.ALZ_running import AlphaSRunner
from zjet_calculator import PhaseSpaceZJet

import vegas
import numpy as np
from typing import Tuple
import multiprocessing
import lhapdf

class ZJet_XS_VegasIntegrand:
    """
    一个为Vegas优化的可调用对象，封装了计算微分截面所需的全部物理逻辑。
    """
    QUARK_FLAVORS_TO_SUM = [1, 2, 3, 4, 5]  # d, u, s, c, b

    def __init__(self, pdf_set: lhapdf.PDF, alpha_s_runner: AlphaSRunner,
                 pT_range: Tuple[float, float], eta_range: Tuple[float, float],
                 kappa_R: float = 1.0, kappa_F: float = 1.0):
        self.pdf_set = pdf_set
        self.alpha_s_runner = alpha_s_runner
        self.kappa_R = kappa_R  # 重整化标度因子
        self.kappa_F = kappa_F  # 因子化标度因子
        self.phase_space = PhaseSpaceZJet(q_beam=2510)
        self.MZ = self.phase_space.MZ
        self.GZ = self.phase_space.GZ
        self.pT_range = pT_range
        self.eta_range = eta_range

    def __call__(self, y: np.ndarray) -> np.ndarray:
        """
        Vegas 调用的核心函数。y 是一个包含4个 [0,1] 范围内随机数的数组。
        """
        y_pt, y_eta, y_v = y
        
        pT = self.pT_range[0] + y_pt * (self.pT_range[1] - self.pT_range[0])
        eta = self.eta_range[0] + y_eta * (self.eta_range[1] - self.eta_range[0])
        jacobian = (self.pT_range[1] - self.pT_range[0]) * (self.eta_range[1] - self.eta_range[0])

        V = self.phase_space.v_var(pT, eta)
        W = self.phase_space.w_var(pT, eta)

        v_min = (V * W)
        if v_min >= 1: return np.array([0.0, 0.0]) # 非物理区域
        v = v_min + y_v * (1 - v_min)
        jacobian *= (1 - v_min)

        w = 1.0

        zc = 1.0

        # --- 1. 运动学计算 ---
        x1 = self.phase_space.x1(v, w, zc, pT, eta)
        x2 = self.phase_space.x2(v, w, zc, pT, eta)
        if not (0 < x1 < 1 and 0 < x2 < 1):
            return np.array([0.0, 0.0])

        shat = self.phase_space.shat(v, w, zc, pT, eta)
        if shat <= 0:
            return np.array([0.0, 0.0])
            
        # --- 2. 标度和耦合常数 ---
        renormalization_scale = self.kappa_R * pT
        factorization_scale = self.kappa_F * pT
        alpha_s = self.alpha_s_runner.alpha_s(renormalization_scale)

        # --- 3. 对所有夸克味求和，计算部分子光度和矩阵元 ---
        lumi_qg, lumi_qqbar = 0.0, 0.0
        matrix_element_qg, matrix_element_qqbar = 0.0, 0.0

        

        for qf in self.QUARK_FLAVORS_TO_SUM:
            # PDF 光度
            q, g = qf, 21
            pdf_qg_pair = self.pdf_set.xfxQ(q, x1, factorization_scale) * self.pdf_set.xfxQ(g, x2, factorization_scale) + \
                          self.pdf_set.xfxQ(g, x1, factorization_scale) * self.pdf_set.xfxQ(q, x2, factorization_scale)
            pdf_qbar_g_pair = self.pdf_set.xfxQ(-q, x1, factorization_scale) * self.pdf_set.xfxQ(g, x2, factorization_scale) + \
                              self.pdf_set.xfxQ(g, x1, factorization_scale) * self.pdf_set.xfxQ(-q, x2, factorization_scale)
            pdf_qqbar_pair = self.pdf_set.xfxQ(q, x1, factorization_scale) * self.pdf_set.xfxQ(-q, x2, factorization_scale) + \
                             self.pdf_set.xfxQ(-q, x1, factorization_scale) * self.pdf_set.xfxQ(q, x2, factorization_scale)
            
            z_coupling = self.phase_space.vq_plus_aq(qf)
            lumi_qg += (pdf_qg_pair + pdf_qbar_g_pair) * z_coupling
            lumi_qqbar += pdf_qqbar_pair * z_coupling

        # 硬矩阵元 (LO)
        uhat = self.phase_space.u(pT, eta) * x2 / zc
        that = self.phase_space.t(pT, eta) * x1 / zc 
        
        if uhat == 0 or that == 0:
            return np.array([0.0, 0.0])

        me_qg_num = shat**2 + uhat**2 - 2.0 * self.MZ**2 * that
        matrix_element_qg = -1.0 / 12.0 * (me_qg_num / (shat * uhat))
        
        me_qqbar_num = that**2 + uhat**2 + 2.0 * self.MZ**2 * shat
        matrix_element_qqbar = 2.0 / 9.0 * (me_qqbar_num / (that * uhat))

        # --- 4. 组合得到微分截面 ---
        common_factor = (alpha_s / (8.0 * shat)) * self.GZ**4 * 2 /(pT*zc**2) *0.3893792922e9
        dsig_qg = lumi_qg * matrix_element_qg * common_factor
        dsig_qqbar = lumi_qqbar * matrix_element_qqbar * common_factor
        
        # 返回一个向量，vegas会分别对每个分量进行积分
        return np.array([dsig_qg, dsig_qqbar])


class CrossSectionCalculator:
    """
    一个高级接口，用于管理和执行 Z+jet 散射截面的计算。
    """
    def __init__(self, pdf_name: str):
        print("Initializing Cross Section Calculator...")
        try:
            self.pdf_set = lhapdf.mkPDF(pdf_name)
        except Exception as e:
            print(f"错误: 无法加载LHAPDF集 '{pdf_name}'. 请确保已正确安装。")
            raise e

        self.alpha_s_runner = AlphaSRunner()
        print("Initialization complete.")

    def calculate(self, 
                  pT_range: Tuple[float, float], 
                  eta_range: Tuple[float, float],
                  scale_factors: Tuple[float, float] = (1.0, 1.0),
                  vegas_params: dict = {'nitn': 10, 'neval': 50000}) -> vegas.Integrator:
        """
        执行完整的截面计算。

        Args:
            pT_range (Tuple[float, float]): Jet的横动量积分范围, e.g., (20, 40)。
            eta_range (Tuple[float, float]): Jet的快度积分范围, e.g., (-0.5, 0.5)。
            scale_factors (Tuple[float, float]): (kappa_R, kappa_F) 标度变化因子。
            vegas_params (dict): 传递给Vegas积分器的参数。

        Returns:
            vegas.Integrator: 完成计算后的Vegas结果对象。
        """
        print(f"\nStarting calculation for pT in {pT_range} GeV, eta in {eta_range}...")
        print(f"Scale factors: kappa_R={scale_factors[0]}, kappa_F={scale_factors[1]}")
    
        # 1. 创建被积函数实例
        integrand = ZJet_XS_VegasIntegrand(
            pdf_set=self.pdf_set,
            alpha_s_runner=self.alpha_s_runner,
            pT_range=pT_range,
            eta_range=eta_range,
            kappa_R=scale_factors[0],
            kappa_F=scale_factors[1]
        )

        integration_bounds = [[0, 1], [0, 1], [0, 1]]

        # 3. 设置并运行Vegas积分

        num_processes = vegas_params.pop('processes', None)

        integ = vegas.Integrator(integration_bounds)
        result = integ(integrand, **vegas_params)
        
        print("\n--- Calculation Finished ---")
        print(result.summary())
        
        return result

if __name__ == "__main__":
    try:
        # 1. 创建总指挥的实例
        calculator = CrossSectionCalculator(pdf_name="CT10nlo")

        # 2. 调用其 calculate 方法执行计算
        #    这将计算 quark-gluon 和 quark-antiquark 两个通道的贡献
        final_result = calculator.calculate(
            pT_range=(20.0, 40.0),
            eta_range=(-0.5, 0.5),
            scale_factors=(1.0, 1.0),  # 使用中心标度
            vegas_params={'nitn': 15, 'neval': 100000,'processes': 16} # 提高精度
        )
        
        # 3. 从结果中提取信息
        qg_contribution = final_result[0]    # 第一个分量对应 Quark-Gluon
        qqbar_contribution = final_result[1] # 第二个分量对应 Quark-Antiquark
        total_cross_section = qg_contribution + qqbar_contribution

        print("\n--- Final Cross Section Results ---")
        print(f"Quark-Gluon Channel      : {qg_contribution}")
        print(f"Quark-Antiquark Channel  : {qqbar_contribution}")
        print("-" * 40)
        print(f"Total Z+jet Cross Section: {total_cross_section}")

    except Exception as e:
        print(f"\nAn error occurred during execution: {e}")