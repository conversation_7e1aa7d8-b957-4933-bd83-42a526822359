# 导入所需的库
from Notebook.Pre.Python.ALZ_running import AlphaSRunner
# Assuming zjet_calculator.PhaseSpaceZJet provides the necessary kinematic functions
# like x1, x2, shat, which are common for 2->2 processes.
from Notebook.EEC_Fit.phase_space_zjet import PhaseSpaceZJet as PhaseSpaceCalculator
from EEC_Fit.evolution_matrix import EvolutionMatrix

import os
import multiprocessing
import numpy as np
import lhapdf
import vegas
from typing import Tuple
from enum import Enum, auto
import math

_PDF_CACHE = {}

os.environ["OMP_NUM_THREADS"] = "1"
os.environ["MKL_NUM_THREADS"] = "1"
os.environ["OPENBLAS_NUM_THREADS"] = "1"


def solve_equations(a1, b1, c1, a2, b2, c2):
    A = np.array([[a1, b1], [a2, b2]])
    B = np.array([c1, c2])

    if np.linalg.det(A) == 0:
        print("方程组无唯一解 (可能无解或有无穷多解)。")
        return None

    try:
        solution = np.linalg.solve(A, B)
        return solution
    except np.linalg.LinAlgError:
        print("计算错误：无法求解。")
        return None

class Channel(Enum):
    qiqj_qX = auto()
    qiqbarj_qX = auto()
    qiqbari_qjX = auto()
    qiqi_qX = auto()
    qiqbari_qiX = auto()
    qiqbari_gX = auto()
    qg_qX = auto()
    qg_gX = auto()
    gg_gX = auto()
    gg_qX = auto()

class PPJet_VegasIntegrand:
    """
    一个为Vegas蒙特卡洛积分器优化的被积函数对象。
    它封装了计算 pp -> jet + X 过程中散射截面所需的所有物理逻辑。
    """
    # Define QCD constants used in the matrix element calculations (from C++)
    NC = 3.0  # Number of colors
    CF = (NC**2 - 1.0) / (2.0 * NC) # Casimir factor for quarks (4/3)
    
    # Active quark flavors to sum over. Using 4 as in the C++ code.
    QUARK_FLAVORS = [1, 2, 3, 4] # d, u, s, c
    ANTI_QUARK_FLAVORS = [-1, -2, -3, -4]
    NF = float(len(QUARK_FLAVORS)) # Number of active flavors (4.0)

    # Define all channels to be calculated
    ALL_CHANNELS = list(Channel)

    def __init__(self, pdf_name: str, alpha_s_runner: AlphaSRunner,
                 pT_range: Tuple[float, float], 
                 eta_range: Tuple[float, float],
                 R: float,
                 kappa_R: float = 1.0, kappa_F: float = 1.0):
        self.pdf_name = pdf_name
        self.alpha_s_runner = alpha_s_runner
        self.kappa_R = kappa_R
        self.kappa_F = kappa_F
        self.pT_range = pT_range
        self.eta_range = eta_range
        self.R = R
        # The phase space calculator is assumed to have the necessary functions
        self.phase_space = PhaseSpaceCalculator(q_beam=6800)
        self.num_channels = len(self.ALL_CHANNELS)

        self.eec_evolver_LL = EvolutionMatrix(
            order=0, kappa=self.kappa_F, alpha_s_runner=alpha_s_runner
        )
        self.eec_evolver_NLL = EvolutionMatrix(
            order=1, kappa=self.kappa_F, alpha_s_runner=alpha_s_runner
        )
        self.eec_evolver_NNLL = EvolutionMatrix(
            order=2, kappa=self.kappa_F, alpha_s_runner=alpha_s_runner
        )

    def __call__(self, y: np.ndarray) -> np.ndarray:
        pid = os.getpid()
        if pid not in _PDF_CACHE:
            lhapdf.setVerbosity(0)
            _PDF_CACHE[pid] = lhapdf.mkPDF(self.pdf_name)
        pdf = _PDF_CACHE[pid]

        y = np.atleast_2d(y)
        N = y.shape[0]
        # Output is now a single value (total cross section), so shape is (N,).
        out = np.empty((N,16), dtype=float)

        for i in range(N):
            out[i] = self._single_event(
                y[i, 0], y[i, 1], y[i, 2], pdf
            )
        return out

    def _calculate_luminosity(self, channel: Channel, pdf, x1, x2, mu):
        """Translates the C++ pdf_prd function."""
        lumi = 0.0
        
        # Use a small epsilon for float comparison
        if x1 <= 0 or x2 <= 0:
            return 0.0

        # Helper lambda to match C++ 'pdf_pair'
        pdf_pair = lambda i, j: pdf.xfxQ(i, x1, mu) * pdf.xfxQ(j, x2, mu)
        
        # All quark and anti-quark flavors
        all_quarks = self.QUARK_FLAVORS + self.ANTI_QUARK_FLAVORS
        g = 21

        if channel == Channel.qiqj_qX: # q_i q_j
            for i in all_quarks:
                for j in all_quarks:
                    if abs(i) != abs(j):
                        lumi += pdf_pair(i, j)
                        
        elif channel == Channel.qiqbarj_qX: # q_i qbar_j
            for i in self.QUARK_FLAVORS:
                for j in self.QUARK_FLAVORS:
                    if i != j:
                        lumi += pdf_pair(i, -j) + pdf_pair(-i, j)

        elif channel == Channel.qiqbari_qjX: # q_i qbar_i (for q_j qbar_j final state)
            for i in self.QUARK_FLAVORS:
                lumi += pdf_pair(i, -i) + pdf_pair(-i, i)

        elif channel == Channel.qiqi_qX: # q_i q_i
            for i in all_quarks:
                lumi += pdf_pair(i, i)

        elif channel == Channel.qiqbari_qiX: # q_i qbar_i (for q_i qbar_i final state)
             for i in self.QUARK_FLAVORS:
                lumi += pdf_pair(i, -i) + pdf_pair(-i, i)

        elif channel == Channel.qiqbari_gX: # q_i qbar_i (for g g final state)
            for i in self.QUARK_FLAVORS:
                lumi += pdf_pair(i, -i) + pdf_pair(-i, i)

        elif channel == Channel.qg_qX or channel == Channel.qg_gX: # q g
            for i in all_quarks:
                lumi += pdf_pair(i, g) + pdf_pair(g, i)

        elif channel == Channel.gg_gX or channel == Channel.gg_qX: # g g
            lumi += pdf_pair(g, g)
        
        return lumi

    def _calculate_matrix_element_sq(self, channel: Channel, v: float):
        """
        Translates the matrix element part of the C++ dsigma_dvdw function.
        Strictly follows the C++ implementation without simplification.
        """
        # Guard against division by zero or invalid v
        epsilon = 1e-9
        if v <= epsilon or v >= 1.0 - epsilon:
            return 0.0

        v2 = v * v
        # v3 = v2 * v
        # v4 = v2 * v2

        one_minus_v = 1.0 - v
        one_minus_v_sq = one_minus_v * one_minus_v

        # Abbreviate for convenience
        NC, CF, NF = self.NC, self.CF, self.NF

        matrix_element_sq = 0.0

        if channel == Channel.qiqj_qX or channel == Channel.qiqbarj_qX:
            # C++: CF / NC * (1 + pow(v, 2)) / pow(1 - v, 2)
            term_v = CF / NC * (1 + v2) / one_minus_v_sq
            # C++: CF / NC * (1 + pow(1 - v, 2)) / pow(v, 2)
            term_one_minus_v = CF / NC * (1 + one_minus_v_sq) / v2
            matrix_element_sq = term_v + term_one_minus_v
            
        elif channel == Channel.qiqbari_qjX:
            # C++: CF / NC * (1.0 + 2 * pow(v, 2) - 2 * v) * (NF - 1.0)
            term_v = CF / NC * (1.0 + 2 * v2 - 2 * v) * (NF - 1.0)
            # C++: CF / NC * (1.0 + 2 * pow(1 - v, 2) - 2 * (1 - v)) * (NF - 1.0)
            term_one_minus_v = CF / NC * (1.0 + 2 * one_minus_v_sq - 2 * one_minus_v) * (NF - 1.0)
            matrix_element_sq = term_v + term_one_minus_v

        elif channel == Channel.qiqi_qX:
            # C++: 2.0 * CF / pow(NC, 2) * (NC * pow(v, 4) - ... ) / pow(1 - v, 2) / pow(v, 2) / 2.0
            num_v = (NC * v**4 - 2.0 * NC * v**3 + 4.0 * NC * v2 + v2 - (3.0 * NC + 1) * v + NC)
            term_v = (2.0 * CF / (NC**2)) * num_v / one_minus_v_sq / v2 / 2.0
            
            # C++: 2.0 * CF / pow(NC, 2) * (NC * pow(1 - v, 4) - ... ) / pow(v, 2) / pow(1 - v, 2) / 2.0
            num_one_minus_v = (NC * one_minus_v**4 - 2.0 * NC * one_minus_v**3 + 4.0 * NC * one_minus_v_sq + one_minus_v_sq - (3.0 * NC + 1) * one_minus_v + NC)
            term_one_minus_v = (2.0 * CF / (NC**2)) * num_one_minus_v / v2 / one_minus_v_sq / 2.0
            
            matrix_element_sq = term_v + term_one_minus_v

        elif channel == Channel.qiqbari_qiX:
            # C++: 2.0 * CF / pow(NC, 2) * (NC * pow(v, 4) - (3.0 * NC + 1.0) * pow(v, 3) + ...) / pow(1 - v, 2)
            num_v = (NC * v**4 - (3.0 * NC + 1.0) * v**3 + (4 * NC + 1.0) * v2 - (2.0 * NC) * v + NC)
            term_v = (2.0 * CF / (NC**2)) * num_v / one_minus_v_sq

            # C++: 2.0 * CF / pow(NC, 2) * (NC * pow(1 - v, 4) - (3.0 * NC + 1.0) * pow(1 - v, 3) + ...) / pow(v, 2)
            num_one_minus_v = (NC * one_minus_v**4 - (3.0 * NC + 1.0) * one_minus_v**3 + (4 * NC + 1.0) * one_minus_v_sq - (2.0 * NC) * one_minus_v + NC)
            term_one_minus_v = (2.0 * CF / (NC**2)) * num_one_minus_v / v2
            
            matrix_element_sq = term_v + term_one_minus_v

        elif channel == Channel.qiqbari_gX:
            # C++ term_v part 1: CF / pow(NC, 2) * (2.0 * pow(v, 2) - 2.0 * v + 1.0)
            p1_v = CF / (NC**2) * (2.0 * v2 - 2.0 * v + 1.0)
            # C++ term_v part 2: (2.0 * pow(NC, 2) * pow(v, 2) - ... - 1.0)
            p2_v = (2.0 * (NC**2) * v2 - 2.0 * (NC**2) * v + (NC**2) - 1.0)
            # C++ term_v full: ... / v / (1 - v) / 2.0
            term_v = p1_v * p2_v / v / one_minus_v / 2.0

            # C++ term_one_minus_v (similar structure)
            p1_omv = CF / (NC**2) * (2.0 * one_minus_v_sq - 2.0 * one_minus_v + 1.0)
            p2_omv = (2.0 * (NC**2) * one_minus_v_sq - 2.0 * (NC**2) * one_minus_v + (NC**2) - 1.0)
            term_one_minus_v = p1_omv * p2_omv / one_minus_v / v / 2.0

            matrix_element_sq = term_v + term_one_minus_v

        elif channel == Channel.qg_qX:
            # C++: (pow(v, 2) + 1.0) * ((pow(NC, 2) - 1.0) * pow(v, 2) + 2.0 * v + pow(NC, 2) - 1.0) / 2.0 / pow(NC, 2) / v / pow(1 - v, 2)
            num = (v2 + 1.0) * ((NC**2 - 1.0) * v2 + 2.0 * v + NC**2 - 1.0)
            matrix_element_sq = num / (2.0 * (NC**2) * v * one_minus_v_sq)

        elif channel == Channel.qg_gX:
            # C++: (pow(1 - v, 2) + 1.0) * ((pow(NC, 2) - 1.0) * pow(1 - v, 2) + 2.0 * (1 - v) + pow(NC, 2) - 1.0) / 2.0 / pow(NC, 2) / (1 - v) / pow(v, 2)
            num = (one_minus_v_sq + 1.0) * ((NC**2 - 1.0) * one_minus_v_sq + 2.0 * one_minus_v + NC**2 - 1.0)
            matrix_element_sq = num / (2.0 * (NC**2) * one_minus_v * v2)

        elif channel == Channel.gg_gX:
            # C++ term_v: (pow(pow(v, 2) - v + 1.0, 3) / pow(v, 2) / pow(1 - v, 2)) * 9.0 / 2.0 / 2.0
            term_v = (pow(v2 - v + 1.0, 3) / v2 / one_minus_v_sq) * 9.0 / 2.0 / 2.0
            
            # C++ term_one_minus_v: (pow(pow(1 - v, 2) - (1 - v) + 1.0, 3) / pow(1 - v, 2) / pow(v, 2)) * 9.0 / 2.0 / 2.0
            term_one_minus_v = (pow(one_minus_v_sq - one_minus_v + 1.0, 3) / one_minus_v_sq / v2) * 9.0 / 2.0 / 2.0
            
            matrix_element_sq = term_v + term_one_minus_v

        elif channel == Channel.gg_qX:
            # C++ common factor: 1 / (2.0 * NC * (pow(NC, 2) - 1.0))
            common_pre = 1.0 / (2.0 * NC * (NC**2 - 1.0))
            
            # C++ term_v: ... * (pow(v, 2) + pow(1 - v, 2)) * (2.0 * pow(NC, 2) * (pow(v, 2) - v) + pow(NC, 2) - 1) / (1 - v) / v * (NF)
            num_v = (v2 + one_minus_v_sq) * (2.0 * NC**2 * (v2 - v) + NC**2 - 1.0)
            term_v = common_pre * num_v / one_minus_v / v * NF

            # C++ term_one_minus_v: ... * (pow(1 - v, 2) + pow(v, 2)) * (2.0 * pow(NC, 2) * (pow(1 - v, 2) - (1 - v)) + pow(NC, 2) - 1) / (1 - v) / v * (NF)
            num_omv = (one_minus_v_sq + v2) * (2.0 * NC**2 * (one_minus_v_sq - one_minus_v) + NC**2 - 1.0)
            term_one_minus_v = common_pre * num_omv / one_minus_v / v * NF

            matrix_element_sq = term_v + term_one_minus_v
        
        return matrix_element_sq


    def _single_event(self, y_pt, y_eta, y_v, pdf):

        channel_results = np.zeros(self.num_channels, dtype=float)
        zero_vector = channel_results.copy()
        # 1. Coordinate transformation
        pT = self.pT_range[0] + y_pt * (self.pT_range[1] - self.pT_range[0])
        eta = self.eta_range[0] + y_eta * (self.eta_range[1] - self.eta_range[0])
        jacobian = (self.pT_range[1] - self.pT_range[0]) * (self.eta_range[1] - self.eta_range[0])

        # For pp->jet+X, zc=1 and w=1 are common choices
        zc = 1.0
        w = 1.0

        # Get integration bounds for v
        V = self.phase_space.v_var(pT, eta)
        W = self.phase_space.w_var(pT, eta)
        v_min = V * W / zc
        v_max = 1.0 - (1.0 - V) / zc
        
        # Check if the phase space point is valid
        if v_min >= 1.0 or v_max <= 0.0 or v_min >= v_max:
             return 0.0
        
        # Map y_v to the (v_min, v_max) interval
        v = v_min + y_v * (v_max - v_min)
        jacobian *= (v_max - v_min)

        # 2. Kinematics calculation
        try:
            x1 = self.phase_space.x1(v, w, zc, pT, eta)
            x2 = self.phase_space.x2(v, w, zc, pT, eta)
            shat = self.phase_space.shat(v, w, zc, pT, eta)
        except Exception:
             # Catch potential errors from phase_space calculations for extreme points
             return 0.0

        if not (0 < x1 < 1 and 0 < x2 < 1): return 0.0
        if shat <= 0: return 0.0
            
        # 3. Scale setting and alpha_s
        mu_R = self.kappa_R * pT
        mu_F = self.kappa_F * pT
        alpha_s = self.alpha_s_runner.alpha_s(mu_R)

        # 4. Sum over all channels
        total_partonic_xs = 0.0
        for i, channel in enumerate(self.ALL_CHANNELS):
            lumi = self._calculate_luminosity(channel, pdf, x1, x2, mu_F)
            if lumi <= 0: continue
            
            me_sq = self._calculate_matrix_element_sq(channel, v)
            if me_sq <= 0: continue
            
            # Combine luminosity and matrix element
            total_partonic_xs += lumi * me_sq
            channel_results[i] = lumi * me_sq

        # 5. Combine all factors for the final differential cross section
        # Factor from C++: (M_PI * alpha_s_sq / (2.0 * shat_val))
        alpha_s_sq = alpha_s * alpha_s
        prefactor = (np.pi * alpha_s_sq / (2.0 * shat))*2 / (pT * zc**2)

        # Conversion factor GeV^-2 to pb
        conversion_factor = 0.3893792922e9

        q_cs = (channel_results[0] + channel_results[1] + channel_results[2] + channel_results[3] + channel_results[4] + channel_results[6] + channel_results[9])* jacobian * conversion_factor*prefactor

        g_cs = (channel_results[8]+channel_results[5]+channel_results[7])* jacobian * conversion_factor*prefactor

        dsig_vec = np.array([q_cs,g_cs])

        q_cs_NLO = q_cs *(1 + alpha_s / 2 / np.pi * AlphaSRunner.CF*(8131/450-13/2 + 91/30 * np.log(self.kappa_F* self.kappa_F)))

        g_cs_NLO = g_cs *( 1 + alpha_s / 2 / np.pi * AlphaSRunner.CA* (411917/22050 - 67/9 -(33716/22050/2*5 -23/9/2*5) + (-181/70* AlphaSRunner.CA-5/3) * np.log(self.kappa_F * self.kappa_F)))
        final_dsig = prefactor * total_partonic_xs* jacobian * conversion_factor

        # q_cs_NLO = q_cs / final_dsig

        # g_cs_NLO = g_cs / final_dsig

        dsig_vec_NLO = np.array([q_cs_NLO,g_cs_NLO])

        Q0 = 50
        U_LL = self.eec_evolver_LL.get_evolution_operator(Q0, pT, self.R, m=0)
        U_NLL = self.eec_evolver_NLL.get_evolution_operator(Q0, pT, self.R, m=2)
        U_NNLL = self.eec_evolver_NNLL.get_evolution_operator(Q0, pT, self.R, m=4)

        A0_pow = self.eec_evolver_LL.get_operator_fit(Q0, self.R)

        A_LL_pow = U_LL @ A0_pow
        A_NLL_pow = U_NLL @ A0_pow
        A_NNLL_pow = U_NNLL @ A0_pow

        a = dsig_vec[0] * A_LL_pow[0][0]
        b = dsig_vec[0] * A_LL_pow[0][1]
        c = dsig_vec[1] * A_LL_pow[1][0]
        d = dsig_vec[1] * A_LL_pow[1][1]

        e = dsig_vec_NLO[0] * A_NLL_pow[0][0]
        f = dsig_vec_NLO[0] * A_NLL_pow[0][1]
        g = dsig_vec_NLO[1] * A_NLL_pow[1][0]
        h = dsig_vec_NLO[1] * A_NLL_pow[1][1]

        i = dsig_vec_NLO[0] * A_NNLL_pow[0][0]
        j = dsig_vec_NLO[0] * A_NNLL_pow[0][1]
        k = dsig_vec_NLO[1] * A_NNLL_pow[1][0]
        l = dsig_vec_NLO[1] * A_NNLL_pow[1][1]



        
        # The final result is d(sigma)/d(pT)d(eta)
        return np.array(
            [   a,
                b,
                c,
                d,
                e,
                f,
                g,
                h,
                i,
                j,
                k,
                l,
                q_cs,
                g_cs,
                q_cs_NLO,
                g_cs_NLO,
            ]
        )



class PPJetCalculator:
    """
    高级接口类，用于管理和执行 pp -> jet+X 散射截面的完整计算流程。
    """
    def __init__(self, pdf_name: str, ALS_MZ: float):
        print(f"正在初始化 pp->jet+X 截面计算器 (PDF: {pdf_name})...")
        try:
            lhapdf.setVerbosity(0)
            lhapdf.mkPDF(pdf_name)
        except Exception as e:
            print(f"错误: 无法加载 PDF 集 '{pdf_name}'.")
            raise e
        
        self.alpha_MZ = ALS_MZ
        self.pdf_name = pdf_name
        self.alpha_s_runner = AlphaSRunner(ALS_MZ=self.alpha_MZ)
        print("初始化完成。")

    def calculate(self, 
                  radius: float,
                  pT_range: Tuple[float, float], 
                  eta_range: Tuple[float, float],
                  scale_factors: Tuple[float, float] = (1.0, 1.0),
                  vegas_params: dict = None) -> vegas.Integrator:
        if vegas_params is None:
            # Increased default neval for better precision in this complex integral
            vegas_params = {'nitn': 10, 'neval': 100000}

        print(f"\n开始计算: pT范围 {pT_range} GeV, eta范围 {eta_range}...")
        print(f"标度因子: kappa_R={scale_factors[0]}, kappa_F={scale_factors[1]}")
    
        integrand = PPJet_VegasIntegrand(
            pdf_name=self.pdf_name,
            alpha_s_runner=self.alpha_s_runner,
            pT_range=pT_range,
            eta_range=eta_range,
            R=radius,
            kappa_R=scale_factors[0],
            kappa_F=scale_factors[1]
        )

        # 3 dimensions: y_pt, y_eta, y_v
        integration_bounds = [[0, 1], [0, 1], [0, 1]]
        integ = vegas.Integrator(integration_bounds)
        
        result = integ(integrand, **vegas_params)
        
        print("\n--- 计算结束 ---")
        # print(result.summary())
        
        return result


if __name__ == "__main__":
    # Set up multiprocessing for parallel VEGAS integration
    multiprocessing.set_start_method('spawn', force=True)


    scale_factor_tuples = [(2.0, 2.0),(0.5, 0.5),(1.0,1.0)]

    base_als_mz = 0.118
    als_mz_ratios = np.linspace(1.00, 1.10, 1)

    full_pt_range = (20, 100)
    pt_bin_width = 5
    pt_bins = np.arange(full_pt_range[0], full_pt_range[1] + 1, pt_bin_width)
    pt_sub_ranges = [(pt_bins[i], pt_bins[i + 1]) for i in range(len(pt_bins) - 1)]

    #for 40-60GeV
    # # 1.0 
# [0.01538306 1.59207897]
# [0.06451072 0.79815391]
    # fit = np.array([0.01726313,1.59244969])
    # fit_NLO = np.array([0.06451072,0.79815391])
# [-0.03398627  2.18930898]
# [-0.10043712  2.14797453]
    # fit = np.array([-0.03398627,2.18930898])
    # fit_NLO = np.array([-0.10043712,2.14797453])
# [0.0285644  1.40040551]
# [0.06850021 0.67031021]
# [0.05053452 1.09138607]
# [0.11509265 0.30673088]
    fit = np.array([0.05053452,1.09138607])
    fit_NLO = np.array([0.11509265,0.30673088])
    ## 1.06
    # fit = np.array([0.04823025,1.00144812])
    # fit_NLO = np.array([0.02848344,0.86170095])


    ## 0.95
    

    # fit = np.array([0.01227155,1.51581212])
    # fit_NLO = np.array([-0.00307854,1.32514967])



    ## 2.0
    # fit = np.array([0.00433392,1.72894017])
    # fit_NLO =np.array([-0.02499845 ,1.36510678])

    ## 0.5
    # fit = np.array([0.05723415,0.79457069])
    # fit_NLO =np.array([0.05723415,0.79457069])



    #for 480-540GeV
    # # 1.0
    # fit = np.array([0.02052015,1.10764108])
    # fit_NLO = np.array([0.02052015,1.10764108])

    # # 1.06


    # 0.94
    # fit = np.array([0.00862476,1.2627197 ])
    # fit_NLO = np.array([0.00862476,1.2627197])

    # for 330-468GeV
    # # 0.02562635 1.14036586
    # fit = np.array([0.02386441,1.14714689])
    # fit_NLO = np.array([0.01154841,1.05578241])
        # fit_NLO = np.array([0.00537008,1.07709336])

    # # 2.0
    # # 0.01301161 1.42818004
    # fit = np.array([.00206433,1.4496269])
    # fit_NLO = np.array([-0.01756468,1.22531013])

    # # 0.5
    # # 0.04225546 0.87674324

    # fit = np.array([0.0410205,0.88214499])
    # fit_NLO = np.array([0.04023545,0.86855456])
    
    # 1.06
   

    # fit = np.array( [0.03487171,1.02398794])
    # fit_NLO = np.array([0.01851481,0.92381518])

    # fit = np.array([0.03622076,1.00238563])
    # fit_NLO = np.array([0.01983325,0.90357081])

# [0.03908717 0.96105956]
# [0.02230618 0.86523047]
    # fit = np.array([0.03908717,0.96105956])
    # fit_NLO = np.array([0.02230618,0.86523047])

    # fit = np.array([0.04126957,0.92229734])
    # fit_NLO = np.array([0.02456036,0.82880453])

    # 0.94
    # fit = np.array([0.01133375,1.31955452])
    # fit_NLO = np.array([0.00960622,1.32237071])





    #13.6

    #1.0
    # fit = np.array([0.02185424,1.5096213])
    # fit_NLO =np.array([0.0041025,1.30680839])
# [0.02111095 1.51681308]
# [0.0041025  1.30680839]
    #2.0
    # fit = np.array([-0.01739074,2.12558605])
    # fit_NLO =np.array([-0.04427016,1.69310418])

    #0.5
    # fit = np.array([0.05626588,0.99499382])
    # fit_NLO =np.array([0.0545792,0.9767642])

    #1.05
    # fit = np.array([0.03876932,1.2736851 ])
    # fit_NLO =np.array([0.01874869,1.09609563])
# [0.03878394 1.27340236]
# [0.01873789 1.09617467]

    # try:
    #     # 1. Initialize the calculator
    #     # calculator = PPJetCalculator(pdf_name="CT10nlo", ALS_MZ=0.118)
    #     calculator = PPJetCalculator(pdf_name="NNPDF31_nlo_as_0118", ALS_MZ=0.118*(1.00))

    #     # 2. Define calculation parameters
    #     # pT_min, pT_max = 846, 1101
    #     # pT_min, pT_max = 638, 846
    #     # pT_min, pT_max = 468, 638
    #     # pT_min, pT_max = 330, 468
    #     # pT_min, pT_max = 220, 330
    #     # pT_min, pT_max = 97, 220
    #     # pT_min, pT_max = 1101, 1410
    #     # pT_min, pT_max = 1410,1784
    #     pT_min, pT_max = 40,60
    #     # eta_min, eta_max = -2.1, 2.1
    #     eta_min, eta_max = -0.5, 0.5
    #     jet_radius = 0.4

    #     # VEGAS parameters (adjust for desired precision/speed)
    #     vegas_config = {
    #         'nitn': 10,       # Number of iterations
    #         'neval': 50000,  # Number of evaluations per iteration
    #         'nproc': 6       # Number of parallel processes
    #     }

    #     # 3. Run the calculation
    #     final_result = calculator.calculate(
    #         radius=jet_radius,
    #         pT_range=(pT_min, pT_max),
    #         eta_range=(eta_min, eta_max),
    #         scale_factors=(0.5, 0.5), # Central scale
    #         vegas_params=vegas_config,
    #     )
    #     result = final_result.flatten()
    #     print(result)

    #     print((fit[0]*(result[0]+result[2]) + fit[1]*(result[1]+result[3]))/(result[12]+result[13]))
    #     print((fit_NLO[0]*(result[8]+result[10]) + fit_NLO[1]*(result[9]+result[11]))/(result[12]+result[13]))




    try:
        for ratio in als_mz_ratios:
            current_als_mz = base_als_mz * ratio

            print(f"\n{'='*30}")
            print(
                f"开始计算 ALPHA_S(MZ) RATIO = {ratio:.3f} (alpha_s = {current_als_mz:.4f})"
            )
            print(f"{'='*30}")
            # calculator = PPJetCalculator(pdf_name="CT10nlo", ALS_MZ=current_als_mz)
            calculator = PPJetCalculator(pdf_name="NNPDF31_nlo_as_0118", ALS_MZ=current_als_mz)



            filename_all = f"PP_13.6_test_2.0_{ratio:.3f}.txt"

            with open(filename_all, "w")  as f_all:

                header = "# pT_center\tEEC_LL\tEEC_NLL\tEEC_NNLL\n"
                f_all.write(header)
                
                for pT_range in pt_sub_ranges:
                    pT_center = (pT_range[0] + pT_range[1]) / 2.0
                    print(f"\n--- 计算 pT 范围: {pT_range} GeV (ratio={ratio:.3f}) ---")

                    result = calculator.calculate(
                        radius=0.4,
                        pT_range=pT_range,
                        eta_range=(-0.5, 0.5),
                        scale_factors=(0.5, 0.5),
                        vegas_params={"nitn": 15, "neval": 50000, "nproc": 6},
                    )

                    result_for_bin = result.flatten()

                    print(f"--- pT 范围 {pT_range} (ratio={ratio:.3f}) 计算完成 ---")

                    cs_q = result_for_bin[12]
                    cs_g = result_for_bin[13]
                    cs_q_NLO = result_for_bin[14]
                    cs_g_NLO = result_for_bin[15]


                    total_cs = cs_q + cs_g
                    print(total_cs)
                    total_cs_NLO = cs_q_NLO + cs_g_NLO
                    if total_cs.mean != 0:
                        eec_ll_all = (
                            fit[0] * (result_for_bin[0] + result_for_bin[2])
                            + fit[1] * (result_for_bin[1] + result_for_bin[3])
                        ) / total_cs
                        eec_nll_all = (
                            fit_NLO[0] * (result_for_bin[4] + result_for_bin[6])
                            + fit_NLO[1] * (result_for_bin[5] + result_for_bin[7])
                        ) / total_cs
                        eec_nnll_all = (
                            fit_NLO[0] * (result_for_bin[8] + result_for_bin[10])
                            + fit_NLO[1] * (result_for_bin[9] + result_for_bin[11])
                        ) / total_cs
                        f_all.write(
                            f"{pT_center:.2f}\t{eec_ll_all.mean:.6e}\t{eec_nll_all.mean:.6e}\t{eec_nnll_all.mean:.6e}\n"
                        )

                        f_all.flush()

                    print(f"pT={pT_center} GeV 的结果已写入文件。")

            print(f"\nALPHA_S(MZ) RATIO = {ratio:.3f} 的所有 pT 箱计算完成。")
            print(f"文件已生成:{filename_all}")

        print(f"\n\n{'='*30}")
        print("所有计算任务已完成！")

    # try:
    #     for sf_tuple in scale_factor_tuples:
    #         print(f"\n{'='*30}")
    #         print(f"开始计算标度因子 kappa_R={sf_tuple[0]}, kappa_F={sf_tuple[1]}")
    #         print(f"{'='*30}")

    #         calculator = PPJetCalculator(pdf_name="CT10nlo", ALS_MZ=base_als_mz)
    #         # calculator = EECFitCalculator(pdf_name="NNPDF23_nlo_as_0118", ALS_MZ=base_als_mz)

    #         fit = np.array([0.004605,1.18525414])
    #         fit_NLO = np.array([0.02278587,1.05557197])
    #         filename_all = f"PP_all_0.5_kR_{sf_tuple[0]:.1f}_kF_{sf_tuple[1]:.1f}.txt"

    #         with open(filename_all, "w") as f_all:

    #             header = "# pT_center\tEEC_LL\tEEC_NLL\tEEC_NNLL\n"
    #             f_all.write(header)

    #             for pT_range in pt_sub_ranges:
    #                 pT_center = (pT_range[0] + pT_range[1]) / 2.0
    #                 print(f"\n--- 计算 pT 范围: {pT_range} GeV (kappa_R={sf_tuple[0]}, kappa_F={sf_tuple[1]}) ---")

    #                 print("计算中，请稍候...")
    #                 result = calculator.calculate(
    #                     radius=0.6,
    #                     pT_range=pT_range,
    #                     eta_range=(-1.0, 1.0),
    #                     scale_factors=sf_tuple,
    #                     vegas_params={"nitn": 10, "neval": 10000, "nproc": 16},
    #                 )

    #                 result_for_bin = result.flatten()

    #                 print(f"--- pT 范围 {pT_range} (kappa_R={sf_tuple[0]}, kappa_F={sf_tuple[1]}) 计算完成 ---")

    #                 cs_q = result_for_bin[12]
    #                 cs_g = result_for_bin[13]
    #                 cs_q_NLO = result_for_bin[14]
    #                 cs_g_NLO = result_for_bin[15]

    #                 print(f"部分子光度: cs_q = {cs_q.mean:.6e}, cs_g = {cs_g.mean:.6e}")

    #                 total_cs = cs_q+ cs_g
    #                 total_cs_NLO = cs_q_NLO + cs_g_NLO
    #                 if total_cs.mean != 0:
    #                     eec_ll_all = (fit[0] * (result_for_bin[0] + result_for_bin[2]) + fit[1] * (result_for_bin[1] + result_for_bin[3])) / total_cs
    #                     eec_nll_all = (fit_NLO[0] * (result_for_bin[4] + result_for_bin[6]) + fit_NLO[1] * (result_for_bin[5] + result_for_bin[7])) / total_cs_NLO
    #                     eec_nnll_all = (fit_NLO[0] * (result_for_bin[8] + result_for_bin[10]) + fit_NLO[1] * (result_for_bin[9] + result_for_bin[11])) / total_cs_NLO
    #                     f_all.write(
    #                         f"{pT_center:.2f}\t{eec_ll_all.mean:.6e}\t{eec_nll_all.mean:.6e}\t{eec_nnll_all.mean:.6e}\n"
    #                     )

    #                     f_all.flush()
    #                 print(f"pT={pT_center} GeV 的结果已写入文件。")
    #         print(f"\n标度因子 kappa_R={sf_tuple[0]}, kappa_F={sf_tuple[1]} 的所有 pT 箱计算完成。")
    #         print(f"文件已生成: {filename_all}")
    #     print(f"\n\n{'='*30}")
    #     print("所有计算任务已完成！")
        
    except Exception as e:
        import traceback
        traceback.print_exc()
        print(f"\n程序执行时发生错误: {e}")