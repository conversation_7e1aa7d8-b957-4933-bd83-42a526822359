import numpy as np
import lhapdf
import vegas
from typing import <PERSON><PERSON>
from scipy.optimize import fsolve
from scipy.linalg import solve_sylvester,expm

import lhapdf
from Notebook.EEC_Fit.phase_space_zjet import PhaseSpaceZJet

pdf = lhapdf.mkPDF("CT10nlo")
# ---------- 群常数 ----------
CA, CF, TR = 3.0, 4.0/3.0, 0.5
Pi = np.pi

#
Zeta2 = 1.64493
Zeta3 = 1.20206
Zeta4 = 1.08232
Zeta5 = 1.03693

# ---------- 阈值 ----------
THRESH = [(1.3, 4), (4.2, 5), (100000, 6)]

def nf_at(Q: float) -> int:
    for m, nf in reversed(THRESH):
        if Q >= m: return nf
    return 3

# ---------- αs 2-loop ----------
M_Z, ALS_MZ = 91.1876, 0.118

def beta0(nf: int) -> float: return 11.0 - 2*nf/3.0
def beta1(nf: int) -> float: return 102.0 - 38*nf/3.0
def beta2(nf: int) -> float: return 2857/2 - 5033/18*nf +325/54*nf*nf

def als_2loop(Q, Q0, als0, nf):
    b0, b1 = beta0(nf), beta1(nf)
    def eq(a): return (1/a + b1/(2*Pi*b0)*np.log(a)
                       - 1/als0 - b1/(2*Pi*b0)*np.log(als0)
                       - b0/(2*Pi)*np.log(Q/Q0))
    a1 = als0/(1 + als0*b0/(2*Pi)*np.log(Q/Q0))
    return fsolve(eq, a1)[0]

def alpha_s(Q: float, kappa: float = 0.5) -> float:
    """
    2-loop αs 跑动，自动过阈值
    返回 α_s(κ·Q)
    """
    Q_scaled = kappa * Q
    nodes = {M_Z, Q_scaled}
    for m, _ in THRESH:
        if min(M_Z, Q_scaled) <= m <= max(M_Z, Q_scaled):
            nodes.add(m)
    path = sorted(nodes, reverse=bool(Q_scaled < M_Z))

    als = ALS_MZ
    for i in range(len(path)-1):
        q1, q2 = path[i], path[i+1]
        als = als_2loop(q2, q1, als, nf_at((q1+q2)/2.0))
    return als

# ---------- 反常维度（原始基底） ----------
def gamma0(nf: int) -> np.ndarray:
    return np.array([[91*CF/15,  -32*nf/105],
                     [-8*CF/15,  181*CA/35 + 2*nf/3]])

print(gamma0(5))

def gamma1(nf: int) -> np.ndarray:
    qq = (-604601*CF*nf/110250+CF*CF*(-474221/13500+24*Zeta2-16*Zeta3)+CA*CF*(520837/6750-544*Zeta2/15+8*Zeta3))
    qg = (-19792*CF*nf)/7875 - (1024*nf**2)/3675 + CA*nf*(1999/18375 + (128*Zeta2)/105)
    gq = CA*CF*(-2882863/661500 - (32*Zeta2)/15) + CF**2*(-9374/3375 + (64*Zeta2)/15)
    gg = (340066*CF*nf)/165375 + CA*nf*(26399/33075 - (16*Zeta2)/3) + CA**2*(4706626/165375 - (632*Zeta2)/105 - 8*Zeta3)
    return np.array([[qq, qg], [gq, gg]])

def gamma2(nf: int) -> np.ndarray:
    
    qq = ((-19521281/13891500)*CF*nf**2 + (-3829448611/33075000 - (5647384*Zeta2)/165375 + (337208*Zeta3)/1575 - (136*Zeta4)/3)*CF**2*nf
    + (-59504161907/4167450000 + (2198426*Zeta2)/33075 - (257336*Zeta3)/1575 + (68*Zeta4)/3)*CA*CF*nf
    + (3623379121/1518750 - (1101667*Zeta2)/1125 - (235402*Zeta3)/75 - 208*Zeta2*Zeta3 + (51064*Zeta4)/15 - 432*Zeta5)*CA*CF**2
    + (-4180165661/16200000 - (730997*Zeta2)/3375 + (281558*Zeta3)/225 + 48*Zeta2*Zeta3 - (12752*Zeta4)/15 + 112*Zeta5)*CA**2*CF
    + (-161877491/81000 + (973994*Zeta2)/1125 + (29756*Zeta3)/15 + 224*Zeta2*Zeta3 - (7312*Zeta4)/3 + 416*Zeta5)*CF**3)

    # qq = (-19521281/13891500)*CF*nf**2

    qg = ((-34936/128625)*nf**3
    + (-304184494/86821875 + (163456*Zeta2)/33075)*CF*nf**2
    + (32725682/5788125 - (7304*Zeta2)/2205 - (128*Zeta3)/63)*CA*nf**2
    + (-89580154991/1041862500 + (1961024*Zeta2)/165375 + (104064*Zeta3)/1225 - (192*Zeta4)/35)*CA*CF*nf
    + (1901298533/119070000 + (18545284*Zeta2)/1157625 - (7552*Zeta3)/735 - (1152*Zeta4)/35)*CA**2*nf
    + (2857982668/37209375 - (154544*Zeta2)/33075 - (1073536*Zeta3)/11025 + (2048*Zeta4)/105)*CF**2*nf)

    
    gq  =((-79552063/25725000 + (196*Zeta2)/225 - (64*Zeta3)/45)*CF**2*nf
    + (-1414558213/12150000 + (91562*Zeta2)/3375 + (3744*Zeta3)/25 - (704*Zeta4)/5)*CF**3
    + (-778603499/22680000 - (8690054*Zeta2)/165375 - (3632*Zeta3)/1575 + (448*Zeta4)/15)*CA**2*CF
    + (359997809/92610000 - (12*Zeta2)/5 + (416*Zeta3)/45)*CA*CF*nf
    + (4485362407/34020000 + (13794439*Zeta2)/165375 - (4160*Zeta3)/21 + (1168*Zeta4)/15)*CA*CF**2)


    gg = ((-(43545391/52093125) - (2048*Zeta2)/4725)*CF*nf**2 
    + (-(110295583/74418750) + (112936*Zeta2)/55125 - (256*Zeta3)/225)*CF**2*nf
    + (-(128595883/41674500) + (160*Zeta2)/27 - (64*Zeta3)/9)*CA*nf**2
    + (-(13864219709/347287500) - (3258284*Zeta2)/165375 + (12672*Zeta3)/175)*CA*CF*nf
    + (-(88337469301/4167450000) - (4654366*Zeta2)/55125 + (2336*Zeta3)/1575 + 104*Zeta4)*CA**2*nf
    +(11996763263/463050000 - (185369599*Zeta2)/1157625 + (698828*Zeta3)/11025 + 64*Zeta2*Zeta3 - (2524*Zeta4)/105 + 96*Zeta5)*CA**3)
    return np.array([[qq, qg], [gq, gg]])




# def gamma2(nf: int) -> np.ndarray:
     
# ---------- U 矩阵递推（原始基底） ----------
def rn(n: int, nf: int) -> np.ndarray:
    g0, g1, g2 = gamma0(nf), gamma1(nf), gamma2(nf)
    b0, b1, b2 = beta0(nf), beta1(nf), beta2(nf)
    # print(g2)
    if n == 0:
        return -g0 / b0
    elif n == 1:
        return (-g1 / b0 - b1 * (-g0) / b0**2)
    elif n == 2:
        return -g2/b0 - b1/b0*(-g1 / b0 - b1/ b0 * (-g0) / b0) - b2/b0*(-g0 / b0)
    else:
        return np.zeros((2, 2))

# def rn(n: int, nf: int) -> np.ndarray:
#     g0, g1, g2 = gamma0(nf), gamma1(nf), gamma2(nf)
#     b0, b1, b2 = beta0(nf), beta1(nf), beta2(nf)
#     if n == 0:
#         return -g0 / b0
#     elif n == 1:
#         return (-g1 / b0 - b1 * (-g0) / b0**2)
#     else:
#         return np.zeros((2, 2))

# def rn(n: int, nf: int) -> np.ndarray:
#     g0, g1, g2 = gamma0(nf), gamma1(nf), gamma2(nf)
#     b0, b1, b2 = beta0(nf), beta1(nf), beta2(nf)
#     if n == 0:
#         return -g0 / b0
#     # elif n == 1:
#     #     return (-g1 / b0 - b1 * (-g0) / b0**2)
#     # elif n == 2:
#     #     return -g2/b0 - b1/b0*(-g1 / b0 - b1/ b0 * (-g0) / b0) - b2/b0*(-g0 / b0)
#     else:
#         return np.zeros((2, 2))

def solve_u_raw(nf: int, n_max: int):
    R, U = {}, {}
    R0 = rn(0, nf)
    I = np.eye(2)
    for n in range(1, n_max+1):
        Rn = rn(n, nf)
        A = -R0 - n*I
        B = R0
        C = Rn + sum(R[n-k] @ U[k] for k in range(1, n))
        U[n] = solve_sylvester(A, B, C)
        R[n] = Rn
    return U

# ---------- S 矩阵（原始基底） ----------
def S_matrix(Q: float, nf: int, m: int, Udict: dict) -> np.ndarray:
    a = alpha_s(Q)/(4*Pi)
    S = np.eye(2)
    for k in range(1, m+1):
        S += a**k * Udict[k]
    return S

# ---------- 单段演化（原始基底） ----------
def evolve_seg(pdf0: np.ndarray, Q0: float, Q: float, nf: int, m: int) -> np.ndarray:
    # 领头阶演化矩阵 L = exp[ ln(a/a0) * (-g0/b0) ]
    # a0, a = alpha_s(Q0)/(4*Pi), alpha_s(Q)/(4*Pi)
    a0, a = 0.118 /(4*Pi), 0.1 /(4*Pi)
    g0 = gamma0(nf)
    b0 = beta0(nf)
    L = expm(np.log(a/a0) * -(-g0/b0))
    # 高阶 S
    U0 = solve_u_raw(nf, m)
    U  = solve_u_raw(nf, m)
    S0 = S_matrix(Q0, nf, m, U0)
    S  = S_matrix(Q, nf, m, U)
    print(L)
    a = np.array([0.1,1])
    return S @ L @ np.linalg.inv(S0) @ pdf0

# ---------- 多段跨阈值 ----------
def evolve_pdf(pdf0: np.ndarray, Q0: float, Q: float, m: int = 2) -> np.ndarray:
    if Q0 == Q: return pdf0
    nodes = {Q0, Q}
    for mth, _ in THRESH:
        if min(Q0, Q) <= mth <= max(Q0, Q): nodes.add(mth)
    path = sorted(nodes, reverse=(Q < Q0))
    pdf = pdf0
    for i in range(len(path)-1):
        q1, q2 = path[i], path[i+1]
        pdf = evolve_seg(pdf, q1, q2, nf_at((q1+q2)/2.0), m)
    return pdf

def _scale_nodes(Q0: float, Q: float, R: float) -> Tuple[list, list]:
    """返回 [Q0*R, ..., Q*R] 节点列表 & 对应 Nf 列表"""
    q0r, q1r = Q0 * R, Q * R
    nodes = {q0r, q1r}
    for mth, _ in THRESH:
        if min(q0r, q1r) <= mth <= max(q0r, q1r):
            nodes.add(mth)
    path = sorted(nodes, reverse=(q1r < q0r))
    nfs  = [nf_at((path[i] + path[i+1])/2.0) for i in range(len(path)-1)]
    return path, nfs

# ---------- 对外唯一接口 ----------
def evolve_pdf_R(pdf0: np.ndarray, Q0: float, Q: float,
                 R: float, m: int = 2) -> np.ndarray:
    """
    从 Q0*R 演化到 Q*R，m 阶截断，Nf 随缩放后能标变动
    """
    if Q0 == Q:
        return pdf0
    path, nfs = _scale_nodes(Q0, Q, R)
    pdf = pdf0
    for i in range(len(path)-1):
        q1, q2 = path[i], path[i+1]
        pdf = evolve_seg(pdf, q1, q2, nfs[i], m)
    return pdf

def integrand(Q_prime, Q0, Q, kappa, pdf_func):
    """
    Q_prime : vegas 积分变量 (标量，Q0 ≤ Q_prime ≤ Q)
    返回 2×1 向量  as(kappa*Q')^(g0/b0) * pdf(Q')
    """
    nf   = nf_at(Q_prime)
    als  = alpha_s(Q_prime*kappa)          # α_s(κQ')
    g0   = gamma0(nf)
    b0   = beta0(nf)
    # 矩阵幂：A^p = expm(p * logm(A))
    A     = als * np.eye(2)                 # 标量乘单位阵 → 可安全幂
    exponent = g0 / b0
    # print(expm(-(alpha_s(40*kappa)/alpha_s(20*kappa)) * exponent))
    A_pow = expm(np.log(als) * exponent)
    # print(g0/b0)
    # print(expm(np.log(0.1 ) * exponent))
    # print(expm( exponent))
    # A_pow= expm(1.0 * exponent)
    # print(A_pow)
    pdf_vec = pdf_func(Q_prime)      # 用户给的 2×1 向量
    return (A_pow).flatten()

# ---------- 对外接口 ----------
def integrate_vegas(Q0: float, Q: float, kappa: float,
                    pdf_func, nint: int = 100, neval: int = 50000):
    """
    pdf_func : callable, 输入 Q'，返回 2×1 numpy 数组
    返回 (2×1 积分结果, vegas 结果对象)
    """
    @vegas.batchintegrand
    def f(x):
        # Qv 形状 (n, 1)
        Qv = x[:, 0]
        n = Qv.size
        out = np.zeros((n, 4))
        out = np.zeros((Qv.shape[0], 4))
        for i, q in enumerate(Qv.flat):
            out[i] = integrand(q, Q0, Q, kappa, pdf_func)
        return out

    integ = vegas.Integrator([[Q0, Q]])
    res_vec = integ(f, nitn=nint, neval=neval, rtol=1e-2)
    return res_vec

def perform_main_task():
    """
    一个示例函数，演示如何使用导入的 PhaseSpaceZJet 类进行计算。
    """
    print("--- Running main calculation task ---")
    
    # 2. 现在你可以像在原始文件中一样，直接使用这个类和它的所有方法
    pt_val = 35.0
    eta_val = 0.1

    print(f"\nUsing imported class for pT={pt_val}, eta={eta_val}:")
    
    # 访问常量
    print(f"Beam energy (Q_beam): {PhaseSpaceZJet.Q_BEAM} GeV")
    
    # 调用静态方法
    s = PhaseSpaceZJet.s()
    t = PhaseSpaceZJet.t(pt_val, eta_val)
    
    print(f"  S = {s:.4e}")
    print(f"  T = {t:.4e}")

    # 计算夸克耦合
    try:
        bottom_coupling = PhaseSpaceZJet.vq_plus_aq(5) # bottom quark
        print(f"  Bottom quark coupling (v_q^2 + a_q^2): {bottom_coupling:.4f}")
    except ValueError as e:
        print(f"An error occurred: {e}")


# 这是程序的入口点
# if __name__ == "__main__":
#     perform_main_task()



# ---------- demo ----------
if __name__ == "__main__":
    R = 0.4
    pdf200 = np.array([1,0.5])
    pdf500 = evolve_pdf_R(pdf200, 30.0, 40.0, R, m=2)

    pdf1000 = evolve_pdf_R(pdf200, 2000.0, 4000.0, R, m=4)
    print(pdf1000)

    # 验证R矩阵
    print(rn(0, 5))
    print(rn(1, 5))
    print(rn(2, 5))
    #验证U矩阵
    print(solve_u_raw(5, 4))

#     # # sigma_500_zjet = np.array([[0.8297,0.1703]])
#     # # sigma_1000_zjet = np.array([[0.79,0.21]])

#     # # print(sigma_500_zjet@pdf500)
#     # # print(sigma_1000_zjet@pdf1000)

#     # # sigma_200_all = np.array([0.2796,0.7204])
#     # # sigma_500_all = np.array([0.3676,0.6324])
#     # # sigma_1000_all = np.array([0.5410,0.4590])

#     # # print(sigma_200_all@pdf200)
#     # # print(sigma_500_all@pdf500)
#     # # print(sigma_1000_all@pdf1000)

#     # R=0.4
#     # # pdf30 = np.array([0.0350,0.0220])
#     # pdf30 = np.array([0.0350,0.0220])
#     # pdf50 = evolve_pdf_R(pdf30, 30.0, 50.0, R, m=2)
#     # # print(pdf50)

#     # NP_EEC = np.array([1.0,1.0])

#     # 示例 pdf_func：常数向量
#     def pdf_const(Q):
#         return np.array([1.0, 1.0])
    
#     a = np.array([0.00755217, 0.000933871])

#     Q0, Q, kappa = 20.0, 40.0, 0.4
#     vegas_result = integrate_vegas(Q0, Q, kappa, pdf_const)
#     print(f"∫_{{{Q0}}}^{{{Q}}} dQ' as({kappa}Q')^(g0/b0) * pdf =")
#     print(vegas_result)
#     # print(vegas_result@a)


#     # print("vegas summary:")
#     # print(vegas_result.summary())
