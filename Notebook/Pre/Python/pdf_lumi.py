import numpy as np
from enum import Enum, auto
from abc import ABC, abstractmethod
from typing import Optional
import lhapdf

# 依赖组件
from Notebook.EEC_Fit.phase_space_zjet import PhaseSpaceZJet

# --- 通用组件 ---
class PartonChannel(Enum):
    QUARK_GLUON = auto()
    QUARK_ANTIQUARK = auto()

# --- 基类与子类 ---

class PDFLumi(ABC):
    """
    【基类】其核心职责是计算部分子光度 (Partonic Luminosity)。
    """
    def __init__(self, pdf_set: lhapdf.PDF):
        if not isinstance(pdf_set, lhapdf.PDF):
            raise TypeError("pdf_set 必须是一个 lhapdf.PDF 对象")
        self.pdf_set = pdf_set
        # print(f"--- PDFLumi 基类初始化成功，使用PDF集: {pdf_set.name} ---")

    def get_lumi(self, channel: PartonChannel, quark_flavor: int, 
                 x1: float, x2: float, mu: float) -> float:
        def pdf_pair(pid1: int, pid2: int) -> float:
            return self.pdf_set.xfxQ(pid1, x1, mu) * self.pdf_set.xfxQ(pid2, x2, mu)

        lumi = 0.0
        q = quark_flavor
        
        if channel == PartonChannel.QUARK_GLUON:
            lumi = (pdf_pair(q, 21) + pdf_pair(21, q) +
                    pdf_pair(-q, 21) + pdf_pair(21, -q))
        elif channel == PartonChannel.QUARK_ANTIQUARK:
            lumi = pdf_pair(q, -q) + pdf_pair(-q, q)
        
        return lumi

    @abstractmethod
    def calculate(self, *args, **kwargs):
        pass

class ZJetCalculator(PDFLumi):
    """
    【子类】专门用于计算 Z+jet 过程。
    """
    # 定义需要求和的夸克味 (d, u, s, c, b)
    QUARK_FLAVORS_TO_SUM = [1, 2, 3, 4, 5]

    def __init__(self, pdf_set: lhapdf.PDF):
        super().__init__(pdf_set)
        self.phase_space = PhaseSpaceZJet()
        print("--- ZJetCalculator 子类初始化完成 ---")

    def _calculate_single_flavor(self, channel: PartonChannel, quark_flavor: int,
                                 v: float, w: float, zc: float,
                                 pT: float, eta: float, mu: float, 
                                 scale_variation: float) -> float:
        """
        【辅助方法】处理单个夸克味的完整计算逻辑。
        """
        x1 = self.phase_space.x1(v, w, zc, pT, eta)
        x2 = self.phase_space.x2(v, w, zc, pT, eta)

        if not (0 < x1 < 1 and 0 < x2 < 1):
            return 0.0

        factorization_scale = scale_variation * mu
        lumi = self.get_lumi(channel, quark_flavor, x1, x2, factorization_scale)
        z_coupling = self.phase_space.vq_plus_aq(quark_flavor)
        
        return lumi * z_coupling

    def calculate(self, channel: PartonChannel,
                  v: float, w: float, zc: float,
                  pT: float, eta: float, mu: float, 
                  scale_variation: float = 1.0,
                  quark_flavor: Optional[int] = None) -> float:
        """
        【主方法】实现 Z+jet 过程的计算。

        Args:
            ...
            quark_flavor (Optional[int]): 要计算的夸克味。
                                           如果为 None (默认)，则对所有味求和。
                                           如果提供整数，则只计算该味的贡献。
        Returns:
            float: 最终的计算结果。
        """
        # 情况一：如果指定了具体的夸克味，则只计算该味
        if quark_flavor is not None:
            return self._calculate_single_flavor(
                channel=channel, quark_flavor=quark_flavor,
                v=v, w=w, zc=zc, pT=pT, eta=eta, mu=mu,
                scale_variation=scale_variation
            )
        
        # 情况二：如果未指定夸克味，则对所有味进行求和
        else:
            total_result = 0.0
            for qf in self.QUARK_FLAVORS_TO_SUM:
                total_result += self._calculate_single_flavor(
                    channel=channel, quark_flavor=qf,
                    v=v, w=w, zc=zc, pT=pT, eta=eta, mu=mu,
                    scale_variation=scale_variation
                )
            return total_result

# --- 示例用法 ---
if __name__ == "__main__":
    try:
        pdf = lhapdf.mkPDF("CT10nlo")
    except Exception as e:
        print(f"无法加载LHAPDF集: {e}")
        exit()
    
    zjet_calc = ZJetCalculator(pdf_set=pdf)

    kin_params = {
        "v": 0.4, "w": 0.5, "zc": 0.8,
        "pT": 35.0, "eta": 0.0, "mu": 35.0
    }

    print("\n--- 模式一: 计算单个味的贡献 ---")
    # 只计算 u 夸克 (flavor=2) 在 qqbar 通道的贡献
    gjet_u_quark_only = zjet_calc.calculate(
        channel=PartonChannel.QUARK_ANTIQUARK,
        quark_flavor=2, # <-- 指定 quark_flavor
        **kin_params
    )
    print(f"Result for QUARK_ANTIQUARK (u-quark ONLY): {gjet_u_quark_only:.6f}")

    print("\n--- 模式二: 默认行为，对所有味求和 ---")
    # 不传入 quark_flavor 参数，计算 qqbar 通道所有味的贡献总和
    gjet_sum_over_flavors = zjet_calc.calculate(
        channel=PartonChannel.QUARK_ANTIQUARK,
        # <-- 未指定 quark_flavor
        **kin_params
    )
    print(f"Result for QUARK_ANTIQUARK (SUM over all flavors): {gjet_sum_over_flavors:.6f}")