#!/usr/bin/env python3
"""
读取EEC_Extract.py输出结果的工具脚本
从txt文件的最后一行提取参数，供EEC_EVO_pp.py使用
"""

import numpy as np
import os


def read_eec_result(filename):
    """
    读取EEC计算结果文件的最后一行
    
    Args:
        filename: 结果文件路径
        
    Returns:
        dict: 包含所有参数的字典
    """
    if not os.path.exists(filename):
        raise FileNotFoundError(f"结果文件不存在: {filename}")
    
    with open(filename, 'r') as f:
        lines = f.readlines()
    
    # 找到最后一个非注释行
    last_line = None
    for line in reversed(lines):
        line = line.strip()
        if line and not line.startswith('#'):
            last_line = line
            break
    
    if last_line is None:
        raise ValueError("文件中没有找到数据行")
    
    # 解析数据
    values = last_line.split()
    if len(values) != 13:
        raise ValueError(f"数据行格式错误，期望13个值，实际得到{len(values)}个")
    
    try:
        result = {
            'Ecom': float(values[0]),
            'radius': float(values[1]),
            'pt_center': float(values[2]),
            'eta_min': float(values[3]),
            'eta_max': float(values[4]),
            'kappa_R': float(values[5]),
            'kappa_F': float(values[6]),
            'LO_fit_0': float(values[7]),
            'LO_fit_1': float(values[8]),
            'NLO_fit_0': float(values[9]),
            'NLO_fit_1': float(values[10]),
            'result_12': float(values[11]),
            'result_13': float(values[12])
        }
        
        # 创建numpy数组
        result['fit'] = np.array([result['LO_fit_0'], result['LO_fit_1']])
        result['fit_NLO'] = np.array([result['NLO_fit_0'], result['NLO_fit_1']])
        result['eta_range'] = (result['eta_min'], result['eta_max'])
        
        return result
        
    except ValueError as e:
        raise ValueError(f"数据转换错误: {e}")


def generate_evo_input_from_result(result_file, output_file="evo_input_from_result.py"):
    """
    从结果文件生成EEC_EVO_pp.py可用的输入文件
    
    Args:
        result_file: EEC_Extract.py的输出文件
        output_file: 生成的Python输入文件
    """
    result = read_eec_result(result_file)
    
    with open(output_file, 'w') as f:
        f.write("# EEC演化计算输入参数\n")
        f.write(f"# 从结果文件生成: {result_file}\n")
        f.write("#\n")
        f.write("import numpy as np\n")
        f.write("\n")
        f.write("# 物理参数\n")
        f.write(f"Ecom = {result['Ecom']}\n")
        f.write(f"radius = {result['radius']}\n")
        f.write(f"pt_center = {result['pt_center']}\n")
        f.write(f"eta_range = ({result['eta_min']}, {result['eta_max']})\n")
        f.write(f"kappa_R = {result['kappa_R']}\n")
        f.write(f"kappa_F = {result['kappa_F']}\n")
        f.write("\n")
        f.write("# 拟合参数\n")
        f.write(f"fit = np.array([{result['LO_fit_0']:.8e}, {result['LO_fit_1']:.8e}])\n")
        f.write(f"fit_NLO = np.array([{result['NLO_fit_0']:.8e}, {result['NLO_fit_1']:.8e}])\n")
        f.write("\n")
        f.write("# 原始结果\n")
        f.write(f"result_12 = {result['result_12']:.8e}\n")
        f.write(f"result_13 = {result['result_13']:.8e}\n")
    
    print(f"EEC_EVO_pp.py输入文件已生成: {output_file}")


def print_result_summary(result_file):
    """
    打印结果文件的摘要信息
    
    Args:
        result_file: EEC_Extract.py的输出文件
    """
    result = read_eec_result(result_file)
    
    print(f"EEC计算结果摘要 (来源: {result_file})")
    print("=" * 50)
    print(f"物理参数:")
    print(f"  质心系能量: {result['Ecom']} GeV")
    print(f"  喷注半径: {result['radius']}")
    print(f"  pT中点: {result['pt_center']} GeV")
    print(f"  eta范围: {result['eta_min']} to {result['eta_max']}")
    print(f"  标度因子: kappa_R={result['kappa_R']}, kappa_F={result['kappa_F']}")
    print()
    print(f"拟合结果:")
    print(f"  LO拟合: [{result['LO_fit_0']:.6e}, {result['LO_fit_1']:.6e}]")
    print(f"  NLO拟合: [{result['NLO_fit_0']:.6e}, {result['NLO_fit_1']:.6e}]")
    print()
    print(f"原始结果:")
    print(f"  result[12]: {result['result_12']:.6e}")
    print(f"  result[13]: {result['result_13']:.6e}")


def main():
    """主函数，演示如何使用这些功能"""
    import sys
    
    if len(sys.argv) < 2:
        print("用法: python read_eec_results.py <结果文件> [操作]")
        print("操作选项:")
        print("  summary  - 显示结果摘要 (默认)")
        print("  generate - 生成EEC_EVO_pp.py输入文件")
        return
    
    result_file = sys.argv[1]
    operation = sys.argv[2] if len(sys.argv) > 2 else "summary"
    
    try:
        if operation == "summary":
            print_result_summary(result_file)
        elif operation == "generate":
            output_file = f"evo_input_from_{os.path.basename(result_file).replace('.txt', '.py')}"
            generate_evo_input_from_result(result_file, output_file)
            print_result_summary(result_file)
        else:
            print(f"未知操作: {operation}")
            
    except Exception as e:
        print(f"错误: {e}")


if __name__ == "__main__":
    main()
