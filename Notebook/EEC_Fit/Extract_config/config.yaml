# EEC计算配置文件
# 用于配置EEC_Extract.py的运行参数

# PDF和强耦合常数设置
pdf_settings:
  pdf_name: "NNPDF31_nlo_as_0118"
  alpha_s_mz: 0.118

# 物理参数设置
physics_parameters:
  # 质心系能量 (GeV)
  center_of_mass_energy: 5020
  
  # 喷注半径
  jet_radius: 0.4
  
  # 横动量范围 (GeV)
  pt_range:
    min: 40
    max: 60
  
  # 快度范围
  eta_range:
    min: -0.5
    max: 0.5
  
  # 标度因子 (kappa_R, kappa_F)
  scale_factors:
    kappa_r: 1.0
    kappa_f: 1.0

# Vegas积分参数
vegas_integration:
  # 迭代次数
  iterations: 10
  
  # 每次迭代的评估点数
  evaluations: 50000
  
  # 并行进程数
  processes: 16

# 数据处理参数
data_processing:
  # 高度数组，用于线性方程组求解
  height_array: [0.0282, 0.0149]
  
  # 输出设置
  output:
    # 是否打印详细结果
    verbose: true
    
    # 是否保存结果到文件
    save_results: true
    
    # 结果文件路径（如果save_results为true）
    output_file: "results.txt"

# 多进程设置
multiprocessing:
  # 启动方法: "spawn", "fork", "forkserver"
  start_method: "spawn"
  
  # 是否强制设置启动方法
  force_start_method: true

# 环境变量设置
environment:
  # OpenMP线程数
  omp_num_threads: 1
  
  # MKL线程数
  mkl_num_threads: 1
  
  # OpenBLAS线程数
  openblas_num_threads: 1
