#!/usr/bin/env python3
"""
EEC_EVO_pp.py 命令行参数使用示例
展示如何使用不同的 alpha_s(MZ) 倍数和标度因子
"""

import subprocess
import sys
import os

def run_eec_evo_command(result_file, alpha_ratio, scale_factor, **kwargs):
    """
    运行EEC_EVO_pp.py命令
    
    Args:
        result_file: EEC结果文件路径
        alpha_ratio: alpha_s(MZ)倍数
        scale_factor: 标度因子
        **kwargs: 其他可选参数
    """
    cmd = [
        sys.executable, "EEC_EVO_pp.py", 
        result_file,
        "-M", str(alpha_ratio),
        "-r", str(scale_factor)
    ]
    
    # 添加其他可选参数
    if 'pdf' in kwargs:
        cmd.extend(["--pdf", kwargs['pdf']])
    if 'Ecom' in kwargs:
        cmd.extend(["--Ecom", str(kwargs['Ecom'])])
    if 'radius' in kwargs:
        cmd.extend(["--radius", str(kwargs['radius'])])
    if 'eta_range' in kwargs:
        cmd.extend(["--eta-range", str(kwargs['eta_range'][0]), str(kwargs['eta_range'][1])])
    if 'pt_range' in kwargs:
        cmd.extend(["--pt-range", str(kwargs['pt_range'][0]), str(kwargs['pt_range'][1])])
    if 'vegas_nitn' in kwargs:
        cmd.extend(["--vegas-nitn", str(kwargs['vegas_nitn'])])
    if 'vegas_neval' in kwargs:
        cmd.extend(["--vegas-neval", str(kwargs['vegas_neval'])])
    if 'vegas_nproc' in kwargs:
        cmd.extend(["--vegas-nproc", str(kwargs['vegas_nproc'])])
    
    print(f"执行命令: {' '.join(cmd)}")
    return cmd

def example_basic_usage():
    """基本使用示例"""
    print("=" * 60)
    print("示例1: 基本使用")
    print("=" * 60)
    
    result_file = "test_result.txt"
    
    # 示例1: 使用默认参数
    print("\n1.1 使用默认参数 (alpha_ratio=1.0, scale_factor=1.0)")
    cmd = run_eec_evo_command(result_file, 1.0, 1.0)
    print("等效命令:")
    print(f"python EEC_EVO_pp.py {result_file}")
    
    # 示例2: 调整alpha_s(MZ)
    print("\n1.2 调整alpha_s(MZ)为原值的0.9倍")
    cmd = run_eec_evo_command(result_file, 0.9, 1.0)
    
    # 示例3: 调整标度因子
    print("\n1.3 使用标度因子2.0")
    cmd = run_eec_evo_command(result_file, 1.0, 2.0)
    
    # 示例4: 同时调整两个参数
    print("\n1.4 alpha_s(MZ)×0.9, 标度因子=0.5")
    cmd = run_eec_evo_command(result_file, 0.9, 0.5)

def example_advanced_usage():
    """高级使用示例"""
    print("\n" + "=" * 60)
    print("示例2: 高级参数设置")
    print("=" * 60)
    
    result_file = "test_result.txt"
    
    # 示例1: 覆盖物理参数
    print("\n2.1 覆盖物理参数")
    cmd = run_eec_evo_command(
        result_file, 1.0, 1.0,
        Ecom=13600,
        radius=0.6,
        eta_range=(-2.0, 2.0),
        pt_range=(50, 100)
    )
    
    # 示例2: 调整Vegas参数
    print("\n2.2 调整Vegas积分参数")
    cmd = run_eec_evo_command(
        result_file, 1.0, 1.0,
        vegas_nitn=20,
        vegas_neval=10000,
        vegas_nproc=8
    )
    
    # 示例3: 使用不同PDF集
    print("\n2.3 使用不同PDF集")
    cmd = run_eec_evo_command(
        result_file, 1.0, 1.0,
        pdf="CT18NLO"
    )

def example_parameter_scan():
    """参数扫描示例"""
    print("\n" + "=" * 60)
    print("示例3: 参数扫描")
    print("=" * 60)
    
    result_file = "test_result.txt"
    
    # alpha_s(MZ)扫描
    print("\n3.1 alpha_s(MZ)扫描")
    alpha_ratios = [0.8, 0.9, 1.0, 1.1, 1.2]
    for ratio in alpha_ratios:
        print(f"  alpha_s(MZ) × {ratio}")
        cmd = run_eec_evo_command(result_file, ratio, 1.0)
        print(f"  输出文件: EEC_EVO_M{ratio:.2f}_r1.0.txt")
    
    # 标度因子扫描
    print("\n3.2 标度因子扫描")
    scale_factors = [0.5, 1.0, 2.0]
    for factor in scale_factors:
        print(f"  标度因子 = {factor}")
        cmd = run_eec_evo_command(result_file, 1.0, factor)
        print(f"  输出文件: EEC_EVO_M1.00_r{factor:.1f}.txt")
    
    # 二维扫描
    print("\n3.3 二维参数扫描")
    alpha_ratios = [0.9, 1.0, 1.1]
    scale_factors = [0.5, 1.0, 2.0]
    
    print("参数组合:")
    for alpha in alpha_ratios:
        for scale in scale_factors:
            print(f"  alpha_s(MZ) × {alpha}, 标度因子 = {scale}")
            cmd = run_eec_evo_command(result_file, alpha, scale)
            print(f"    输出文件: EEC_EVO_M{alpha:.2f}_r{scale:.1f}.txt")

def example_batch_script():
    """生成批处理脚本示例"""
    print("\n" + "=" * 60)
    print("示例4: 生成批处理脚本")
    print("=" * 60)
    
    script_content = """#!/bin/bash
# EEC_EVO_pp.py 批处理脚本
# 自动生成的参数扫描脚本

RESULT_FILE="Extract_config/results.txt"

echo "开始EEC演化计算参数扫描"
echo "=========================="

# alpha_s(MZ)扫描
echo "1. alpha_s(MZ)扫描"
for ratio in 0.8 0.9 1.0 1.1 1.2; do
    echo "  运行 alpha_s(MZ) × $ratio"
    python EEC_EVO_pp.py $RESULT_FILE -M $ratio -r 1.0
    echo "  完成: EEC_EVO_M${ratio}_r1.0.txt"
done

# 标度因子扫描
echo "2. 标度因子扫描"
for factor in 0.5 1.0 2.0; do
    echo "  运行标度因子 = $factor"
    python EEC_EVO_pp.py $RESULT_FILE -M 1.0 -r $factor
    echo "  完成: EEC_EVO_M1.00_r${factor}.txt"
done

echo "所有计算完成!"
"""
    
    with open("batch_eec_evo.sh", "w") as f:
        f.write(script_content)
    
    print("批处理脚本已生成: batch_eec_evo.sh")
    print("使用方法:")
    print("  chmod +x batch_eec_evo.sh")
    print("  ./batch_eec_evo.sh")

def show_help():
    """显示帮助信息"""
    print("\n" + "=" * 60)
    print("EEC_EVO_pp.py 命令行参数说明")
    print("=" * 60)
    
    help_text = """
基本语法:
    python EEC_EVO_pp.py [结果文件] [选项]

主要参数:
    结果文件              EEC_Extract.py的输出文件路径
    -M, --alpha-ratio     alpha_s(MZ)的倍数 (默认: 1.0)
    -r, --scale-factor    标度因子 (默认: 1.0)

物理参数 (可选，覆盖结果文件中的值):
    --pdf                 PDF集名称
    --Ecom                质心系能量 (GeV)
    --radius              喷注半径
    --eta-range           eta范围 (min max)
    --pt-range            pT范围 (min max)

Vegas积分参数:
    --vegas-nitn          迭代次数 (默认: 15)
    --vegas-neval         每次迭代评估点数 (默认: 5000)
    --vegas-nproc         并行进程数 (默认: 16)

使用示例:
    # 基本使用
    python EEC_EVO_pp.py results.txt

    # 调整alpha_s(MZ)为原值的0.9倍
    python EEC_EVO_pp.py results.txt -M 0.9

    # 使用标度因子2.0
    python EEC_EVO_pp.py results.txt -r 2.0

    # 同时调整两个参数
    python EEC_EVO_pp.py results.txt -M 0.9 -r 2.0

    # 覆盖物理参数
    python EEC_EVO_pp.py results.txt -M 1.0 -r 1.0 --Ecom 13600 --radius 0.6

输出文件:
    输出文件名格式: EEC_EVO_M{alpha_ratio:.2f}_r{scale_factor:.1f}.txt
    例如: EEC_EVO_M0.90_r2.0.txt
"""
    print(help_text)

def main():
    """主函数"""
    print("EEC_EVO_pp.py 命令行参数使用示例")
    
    # 检查是否存在测试文件
    if not os.path.exists("test_result.txt"):
        print("警告: 测试文件 test_result.txt 不存在")
        print("请先运行 EEC_Extract.py 生成结果文件")
    
    # 显示各种使用示例
    example_basic_usage()
    example_advanced_usage()
    example_parameter_scan()
    example_batch_script()
    show_help()
    
    print(f"\n{'='*60}")
    print("示例完成!")
    print("现在可以根据需要运行相应的命令")
    print(f"{'='*60}")

if __name__ == "__main__":
    main()
