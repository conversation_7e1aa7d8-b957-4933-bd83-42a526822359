from ALZ_running import AlphaSRunner

from phase_space_zjet import PhaseSpaceZJet as PhaseSpaceCalculator
from evolution_matrix import EvolutionMatrix

import os
import multiprocessing
import numpy as np
import lhapdf
import vegas
from typing import Tuple


_PDF_CACHE = {}

os.environ["OMP_NUM_THREADS"] = "1"
os.environ["MKL_NUM_THREADS"] = "1"
os.environ["OPENBLAS_NUM_THREADS"] = "1"

class ZJet_EEC_VegasIntegrand:
    QUARK_FLAVORS_TO_SUM = [1, 2, 3, 4, 5]

    def __init__(
        self,
        pdf_name: str,
        ALZ_runner: AlphaSRunner,
        Ecom: float,
        pT_range: Tuple[float, float],
        eta_range: Tuple[float, float],
        R: float,
        kappa_R: float = 1.0,
        kappa_F: float = 1.0,
    ):

        self.pdf_name = pdf_name
        self.ALZ_runner = ALZ_runner
        self.kappa_R = kappa_R
        self.kappa_F = kappa_F
        self.pT_range = pT_range
        self.eta_range = eta_range
        self.R = R
        self.phase_space = PhaseSpaceCalculator(Ecom=Ecom)

        self.eec_evolver_LL = EvolutionMatrix(
            order=0, kappa=self.kappa_F, ALZ_runner=ALZ_runner
        )
        self.eec_evolver_NLL = EvolutionMatrix(
            order=1, kappa=self.kappa_F, ALZ_runner=ALZ_runner
        )
        self.eec_evolver_NNLL = EvolutionMatrix(
            order=2, kappa=self.kappa_F, ALZ_runner=ALZ_runner
        )


        self.MZ = self.phase_space.MZ
        self.GZ = self.phase_space.GZ

    def __call__(self, y: np.ndarray) -> np.ndarray:
        pid = os.getpid()
        if pid not in _PDF_CACHE:
            lhapdf.setVerbosity(0)
            _PDF_CACHE[pid] = lhapdf.mkPDF(self.pdf_name)
        pdf = _PDF_CACHE[pid]

        y = np.atleast_2d(y)
        N = y.shape[0]
        # Output is now a single value (total cross section), so shape is (N,).
        out = np.empty((N, 16), dtype=float)

        for i in range(N):
            out[i] = self._single_event(y[i, 0], y[i, 1], y[i, 2], pdf)
        return out

    def _single_event(self, y_pt, y_eta, y_v, pdf):
        pT = self.pT_range[0] + y_pt * (self.pT_range[1] - self.pT_range[0])
        eta = self.eta_range[0] + y_eta * (self.eta_range[1] - self.eta_range[0])

        jacobian = (self.pT_range[1] - self.pT_range[0]) * (
            self.eta_range[1] - self.eta_range[0]
        )

        zc = 1.0

        V = self.phase_space.v_var(pT, eta)
        W = self.phase_space.w_var(pT, eta)
        v_min = V * W / zc
        v_max = 1.0 - (1.0 - V) / zc

        # 如果v的下限大于等于1，说明该点处于非物理区域，直接返回0。
        if v_min >= 1 or v_max >= 1:
            return np.zeros(16)

        v = v_min + y_v * (v_max - v_min)

        jacobian *= v_max - v_min
        w = 1.0

        x1 = self.phase_space.x1(v, w, zc, pT, eta)
        x2 = self.phase_space.x2(v, w, zc, pT, eta)

        if not (0 < x1 < 1 and 0 < x2 < 1):
            return np.zeros(16)

        # 计算部分子质心系能量平方 shat。
        shat = self.phase_space.shat(v, w, zc, pT, eta)
        # 检查shat是否为正，否则为非物理点。
        if shat <= 0:
            return np.zeros(16)

        # === 3. 标度设置与耦合常数计算 ===
        # 设置重整化标度(mu_R)和因子化标度(mu_F)，通常取为过程的硬标度pT。
        mu_R = self.kappa_R * pT
        mu_F = self.kappa_F * pT
        # 在重整化标度mu_R下计算强耦合常数alpha_s。
        alpha_s = self.ALZ_runner.alpha_s(mu_R)
        # print(alpha_s)

        alpha_ptR = self.ALZ_runner.alpha_s(mu_F)

        # === 4. 部分子光度与矩阵元计算 ===
        # 初始化夸克-胶子(qg)和夸克-反夸克(qqbar)通道的部分子光度。
        lumi_qg, lumi_qqbar = 0.0, 0.0

        # 循环遍历所有参与反应的夸克味。
        for qf in self.QUARK_FLAVORS_TO_SUM:
            # 获取夸克和胶子的PDF ID。
            q, g = qf, 21
            # 获取夸克与Z玻色子的电弱耦合系数。
            z_coupling = self.phase_space.vq_plus_aq(qf)

            # 从PDF对象中获取在标度mu_F和动量分数x1, x2处的PDF值。
            pdf_q_x1 = pdf.xfxQ(q, x1, mu_F)
            pdf_qbar_x1 = pdf.xfxQ(-q, x1, mu_F)
            pdf_g_x1 = pdf.xfxQ(g, x1, mu_F)

            pdf_q_x2 = pdf.xfxQ(q, x2, mu_F)
            pdf_qbar_x2 = pdf.xfxQ(-q, x2, mu_F)
            pdf_g_x2 = pdf.xfxQ(g, x2, mu_F)

            # 计算并累加 qg -> Zq 和 gq -> Zq 过程的光度。
            lumi_qg += (
                (pdf_q_x1 + pdf_qbar_x1) * pdf_g_x2
                + pdf_g_x1 * (pdf_q_x2 + pdf_qbar_x2)
            ) * z_coupling

            # 计算并累加 qqbar -> Zg 过程的光度。
            lumi_qqbar += (pdf_q_x1 * pdf_qbar_x2 + pdf_qbar_x1 * pdf_q_x2) * z_coupling

        # 计算曼德尔斯坦变量 uhat 和 that。
        uhat = self.phase_space.u(pT, eta) * x2 / zc
        that = self.phase_space.t(pT, eta) * x1 / zc

        if uhat == 0 or that == 0:
            return np.zeros(16)

        # 计算领头阶(LO)的硬散射矩阵元。
        me_qg_num = shat**2 + uhat**2 - 2.0 * self.MZ**2 * that
        matrix_element_qg = -1.0 / 12.0 * (me_qg_num / (shat * uhat))

        me_qqbar_num = that**2 + uhat**2 + 2.0 * self.MZ**2 * shat
        matrix_element_qqbar = 2.0 / 9.0 * (me_qqbar_num / (that * uhat))

        common_factor = (
            (alpha_s / (8.0 * shat)) * self.GZ**4 * 2 / (pT * zc**2) * 0.3893792922e9
        )

        # print(alpha_s)

        dsig_qg = lumi_qg * matrix_element_qg * common_factor * jacobian
        dsig_qqbar = lumi_qqbar * matrix_element_qqbar * common_factor * jacobian

        dsig_qg_NLO = dsig_qg * (
            1 + alpha_ptR / 2 / np.pi * AlphaSRunner.CF*(8131/450-13/2 + 91/30 * np.log(self.kappa_F* self.kappa_F))
        )
        dsig_qqbar_NLO = dsig_qqbar * (
            1 + alpha_ptR / 2 / np.pi * AlphaSRunner.CA* (411917/22050 - 67/9 -(33716/22050/2*5 -23/9/2*5) + (-181/70* AlphaSRunner.CA-5/3) * np.log(self.kappa_F * self.kappa_F))
        )
        dsig_vec = np.array([dsig_qg, dsig_qqbar])

        dsig_vec_NLO = np.array([dsig_qg_NLO, dsig_qqbar_NLO])

        Q0 = 37.5
        U_NLL = self.eec_evolver_NLL.get_evolution_operator(Q0, pT, self.R, m=2)
        U_NNLL = self.eec_evolver_NNLL.get_evolution_operator(Q0, pT, self.R, m=4)

        A0_pow = self.eec_evolver_LL.get_operator_fit(Q0, self.R)

        A0_Fit_LL_pow = self.eec_evolver_LL.get_operator_fit(pT, self.R)

        A_NLL_pow = U_NLL @ A0_pow
        A_NNLL_pow = U_NNLL @ A0_pow
        # print(A0_Fit_LL_pow)
        a = dsig_vec[0] * A0_Fit_LL_pow[0][0]
        b = dsig_vec[0] * A0_Fit_LL_pow[0][1]
        c = dsig_vec[1] * A0_Fit_LL_pow[1][0]
        d = dsig_vec[1] * A0_Fit_LL_pow[1][1]

        e = dsig_vec_NLO[0] * A_NLL_pow[0][0]
        f = dsig_vec_NLO[0] * A_NLL_pow[0][1]
        g = dsig_vec_NLO[1] * A_NLL_pow[1][0]
        h = dsig_vec_NLO[1] * A_NLL_pow[1][1]

        i = dsig_vec_NLO[0] * A_NNLL_pow[0][0]
        j = dsig_vec_NLO[0] * A_NNLL_pow[0][1]
        k = dsig_vec_NLO[1] * A_NNLL_pow[1][0]
        l = dsig_vec_NLO[1] * A_NNLL_pow[1][1]

        return np.array(
            [
                a,
                b,
                c,
                d,
                e,
                f,
                g,
                h,
                i,
                j,
                k,
                l,
                dsig_qg,
                dsig_qqbar,
                dsig_qg_NLO,
                dsig_qqbar_NLO,
            ]
        )

        # return (dsig_vec @ A_pow@EEC_NP).flatten()


class EECFitCalculator:
    def __init__(self, pdf_name: str, ALS_MZ: float):
        print(f"正在初始化截面计算器 (PDF: {pdf_name})...")
        try:
            lhapdf.setVerbosity(0)
            lhapdf.mkPDF(pdf_name)
        except Exception as e:
            print(f"错误: 无法加载 PDF 集 '{pdf_name}'.")
            raise e

        self.alpha_MZ = ALS_MZ
        self.pdf_name = pdf_name
        self.ALZ_runner = AlphaSRunner(ALS_MZ=self.alpha_MZ)
        print("初始化完成。")

    def calculate(
        self,
        Ecom: float,
        radius: float,
        pT_range: Tuple[float, float],
        eta_range: Tuple[float, float],
        scale_factors: Tuple[float, float] = (1.0, 1.0),
        vegas_params: dict = None,
    ) -> vegas.Integrator:
        if vegas_params is None:
            # Increased default neval for better precision in this complex integral
            vegas_params = {"nitn": 10, "neval": 100000}

        print(f"\n开始计算: pT范围 {pT_range} GeV, eta范围 {eta_range}...")
        print(f"标度因子: kappa_R={scale_factors[0]}, kappa_F={scale_factors[1]}")

        integrand = ZJet_EEC_VegasIntegrand(
            pdf_name=self.pdf_name,
            ALZ_runner=self.ALZ_runner,
            Ecom=Ecom,
            pT_range=pT_range,
            eta_range=eta_range,
            R=radius,
            kappa_R=scale_factors[0],
            kappa_F=scale_factors[1],
        )

        integration_bounds = [[0, 1], [0, 1], [0, 1]]
        integ = vegas.Integrator(integration_bounds)

        result = integ(integrand, **vegas_params)

        print("\n--- 计算结束 ---")
        return result


if __name__ == "__main__":
    # Set up multiprocessing for parallel VEGAS integration
    multiprocessing.set_start_method("spawn", force=True)
    height=np.array([0.0282,0.0149])
    try:
        calculator = EECFitCalculator(pdf_name="NNPDF31_nlo_as_0118", ALS_MZ=0.118)

        pT_min, pT_max =  40,60
        eta_min, eta_max = -0.5, 0.5
        jet_radius = 0.4

        vegas_config = {
            'nitn': 10,   
            'neval': 50000,  
            'nproc': 16  
        }

        final_result = calculator.calculate(
            Ecom=5020,
            radius=jet_radius,
            pT_range=(pT_min, pT_max),
            eta_range=(eta_min, eta_max),
            scale_factors=(1.0,1.0),
            vegas_params=vegas_config,
        )

        result = final_result.flatten()

        print(result)
        
       
        A = np.array([
            [result[0].mean, result[1].mean],
            [result[2].mean, result[3].mean],
        ])
        print(A)
        B = np.array([height[0]*result[12].mean,height[1]*result[13].mean])

        solution = np.linalg.solve(A, B)

        print(solution)


    except Exception as e:
        print(f"\n程序执行时发生错误: {e}")
        # 打印更详细的追溯信息以帮助调试
        import traceback

        traceback.print_exc()
