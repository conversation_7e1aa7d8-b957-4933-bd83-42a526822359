from ALZ_running import AlphaSRunner

from phase_space_zjet import PhaseSpaceZJet as PhaseSpaceCalculator
from evolution_matrix import EvolutionMatrix

import os
import multiprocessing
import numpy as np
import lhapdf
import vegas
import yaml
from typing import Tuple, Dict, Any


_PDF_CACHE = {}


class ConfigLoader:
    """配置文件加载器，用于从YAML文件读取计算参数"""

    def __init__(self, config_path: str = "config.yaml"):
        """
        初始化配置加载器

        Args:
            config_path: YAML配置文件路径
        """
        self.config_path = config_path
        self.config = self._load_config()
        self._set_environment_variables()

    def _load_config(self) -> Dict[str, Any]:
        """加载YAML配置文件"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as file:
                config = yaml.safe_load(file)
            print(f"成功加载配置文件: {self.config_path}")
            return config
        except FileNotFoundError:
            print(f"警告: 配置文件 {self.config_path} 未找到，使用默认配置")
            return self._get_default_config()
        except yaml.YAMLError as e:
            print(f"错误: 配置文件格式错误: {e}")
            print("使用默认配置")
            return self._get_default_config()

    def _get_default_config(self) -> Dict[str, Any]:
        """返回默认配置"""
        return {
            'pdf_settings': {
                'pdf_name': 'NNPDF31_nlo_as_0118',
                'alpha_s_mz': 0.118
            },
            'physics_parameters': {
                'center_of_mass_energy': 5020,
                'jet_radius': 0.4,
                'pt_range': {'min': 40, 'max': 60},
                'eta_range': {'min': -0.5, 'max': 0.5},
                'scale_factors': {'kappa_r': 1.0, 'kappa_f': 1.0}
            },
            'vegas_integration': {
                'iterations': 10,
                'evaluations': 50000,
                'processes': 16
            },
            'data_processing': {
                'height_array': [0.0282, 0.0149],
                'output': {'verbose': True, 'save_results': False}
            },
            'multiprocessing': {
                'start_method': 'spawn',
                'force_start_method': True
            },
            'environment': {
                'omp_num_threads': 1,
                'mkl_num_threads': 1,
                'openblas_num_threads': 1
            }
        }

    def _set_environment_variables(self):
        """设置环境变量"""
        env_config = self.config.get('environment', {})
        os.environ["OMP_NUM_THREADS"] = str(env_config.get('omp_num_threads', 1))
        os.environ["MKL_NUM_THREADS"] = str(env_config.get('mkl_num_threads', 1))
        os.environ["OPENBLAS_NUM_THREADS"] = str(env_config.get('openblas_num_threads', 1))

    def get_pdf_settings(self) -> Dict[str, Any]:
        """获取PDF设置"""
        return self.config['pdf_settings']

    def get_physics_parameters(self) -> Dict[str, Any]:
        """获取物理参数"""
        return self.config['physics_parameters']

    def get_vegas_parameters(self) -> Dict[str, Any]:
        """获取Vegas积分参数"""
        vegas_config = self.config['vegas_integration']
        return {
            'nitn': vegas_config['iterations'],
            'neval': vegas_config['evaluations'],
            'nproc': vegas_config['processes']
        }

    def get_data_processing_config(self) -> Dict[str, Any]:
        """获取数据处理配置"""
        return self.config['data_processing']

    def get_multiprocessing_config(self) -> Dict[str, Any]:
        """获取多进程配置"""
        return self.config['multiprocessing']

class ZJet_EEC_VegasIntegrand:
    QUARK_FLAVORS_TO_SUM = [1, 2, 3, 4, 5]

    def __init__(
        self,
        pdf_name: str,
        ALZ_runner: AlphaSRunner,
        Ecom: float,
        pT_range: Tuple[float, float],
        eta_range: Tuple[float, float],
        R: float,
        kappa_R: float = 1.0,
        kappa_F: float = 1.0,
    ):

        self.pdf_name = pdf_name
        self.ALZ_runner = ALZ_runner
        self.kappa_R = kappa_R
        self.kappa_F = kappa_F
        self.pT_range = pT_range
        self.eta_range = eta_range
        self.R = R
        self.phase_space = PhaseSpaceCalculator(Ecom=Ecom)

        self.eec_evolver_LL = EvolutionMatrix(
            order=0, kappa=self.kappa_F, ALZ_runner=ALZ_runner
        )
        self.eec_evolver_NLL = EvolutionMatrix(
            order=1, kappa=self.kappa_F, ALZ_runner=ALZ_runner
        )
        self.eec_evolver_NNLL = EvolutionMatrix(
            order=2, kappa=self.kappa_F, ALZ_runner=ALZ_runner
        )


        self.MZ = self.phase_space.MZ
        self.GZ = self.phase_space.GZ

    def __call__(self, y: np.ndarray) -> np.ndarray:
        pid = os.getpid()
        if pid not in _PDF_CACHE:
            lhapdf.setVerbosity(0)
            _PDF_CACHE[pid] = lhapdf.mkPDF(self.pdf_name)
        pdf = _PDF_CACHE[pid]

        y = np.atleast_2d(y)
        N = y.shape[0]
        # Output is now a single value (total cross section), so shape is (N,).
        out = np.empty((N, 16), dtype=float)

        for i in range(N):
            out[i] = self._single_event(y[i, 0], y[i, 1], y[i, 2], pdf)
        return out

    def _single_event(self, y_pt, y_eta, y_v, pdf):
        pT = self.pT_range[0] + y_pt * (self.pT_range[1] - self.pT_range[0])
        eta = self.eta_range[0] + y_eta * (self.eta_range[1] - self.eta_range[0])

        jacobian = (self.pT_range[1] - self.pT_range[0]) * (
            self.eta_range[1] - self.eta_range[0]
        )

        zc = 1.0

        V = self.phase_space.v_var(pT, eta)
        W = self.phase_space.w_var(pT, eta)
        v_min = V * W / zc
        v_max = 1.0 - (1.0 - V) / zc

        # 如果v的下限大于等于1，说明该点处于非物理区域，直接返回0。
        if v_min >= 1 or v_max >= 1:
            return np.zeros(16)

        v = v_min + y_v * (v_max - v_min)

        jacobian *= v_max - v_min
        w = 1.0

        x1 = self.phase_space.x1(v, w, zc, pT, eta)
        x2 = self.phase_space.x2(v, w, zc, pT, eta)

        if not (0 < x1 < 1 and 0 < x2 < 1):
            return np.zeros(16)

        # 计算部分子质心系能量平方 shat。
        shat = self.phase_space.shat(v, w, zc, pT, eta)
        # 检查shat是否为正，否则为非物理点。
        if shat <= 0:
            return np.zeros(16)

        # === 3. 标度设置与耦合常数计算 ===
        # 设置重整化标度(mu_R)和因子化标度(mu_F)，通常取为过程的硬标度pT。
        mu_R = self.kappa_R * pT
        mu_F = self.kappa_F * pT
        # 在重整化标度mu_R下计算强耦合常数alpha_s。
        alpha_s = self.ALZ_runner.alpha_s(mu_R)
        # print(alpha_s)

        alpha_ptR = self.ALZ_runner.alpha_s(mu_F)

        # === 4. 部分子光度与矩阵元计算 ===
        # 初始化夸克-胶子(qg)和夸克-反夸克(qqbar)通道的部分子光度。
        lumi_qg, lumi_qqbar = 0.0, 0.0

        # 循环遍历所有参与反应的夸克味。
        for qf in self.QUARK_FLAVORS_TO_SUM:
            # 获取夸克和胶子的PDF ID。
            q, g = qf, 21
            # 获取夸克与Z玻色子的电弱耦合系数。
            z_coupling = self.phase_space.vq_plus_aq(qf)

            # 从PDF对象中获取在标度mu_F和动量分数x1, x2处的PDF值。
            pdf_q_x1 = pdf.xfxQ(q, x1, mu_F)
            pdf_qbar_x1 = pdf.xfxQ(-q, x1, mu_F)
            pdf_g_x1 = pdf.xfxQ(g, x1, mu_F)

            pdf_q_x2 = pdf.xfxQ(q, x2, mu_F)
            pdf_qbar_x2 = pdf.xfxQ(-q, x2, mu_F)
            pdf_g_x2 = pdf.xfxQ(g, x2, mu_F)

            # 计算并累加 qg -> Zq 和 gq -> Zq 过程的光度。
            lumi_qg += (
                (pdf_q_x1 + pdf_qbar_x1) * pdf_g_x2
                + pdf_g_x1 * (pdf_q_x2 + pdf_qbar_x2)
            ) * z_coupling

            # 计算并累加 qqbar -> Zg 过程的光度。
            lumi_qqbar += (pdf_q_x1 * pdf_qbar_x2 + pdf_qbar_x1 * pdf_q_x2) * z_coupling

        # 计算曼德尔斯坦变量 uhat 和 that。
        uhat = self.phase_space.u(pT, eta) * x2 / zc
        that = self.phase_space.t(pT, eta) * x1 / zc

        if uhat == 0 or that == 0:
            return np.zeros(16)

        # 计算领头阶(LO)的硬散射矩阵元。
        me_qg_num = shat**2 + uhat**2 - 2.0 * self.MZ**2 * that
        matrix_element_qg = -1.0 / 12.0 * (me_qg_num / (shat * uhat))

        me_qqbar_num = that**2 + uhat**2 + 2.0 * self.MZ**2 * shat
        matrix_element_qqbar = 2.0 / 9.0 * (me_qqbar_num / (that * uhat))

        common_factor = (
            (alpha_s / (8.0 * shat)) * self.GZ**4 * 2 / (pT * zc**2) * 0.3893792922e9
        )

        # print(alpha_s)

        dsig_qg = lumi_qg * matrix_element_qg * common_factor * jacobian
        dsig_qqbar = lumi_qqbar * matrix_element_qqbar * common_factor * jacobian

        dsig_qg_NLO = dsig_qg * (
            1 + alpha_ptR / 2 / np.pi * AlphaSRunner.CF*(8131/450-13/2 + 91/30 * np.log(self.kappa_F* self.kappa_F))
        )
        dsig_qqbar_NLO = dsig_qqbar * (
            1 + alpha_ptR / 2 / np.pi * AlphaSRunner.CA* (411917/22050 - 67/9 -(33716/22050/2*5 -23/9/2*5) + (-181/70* AlphaSRunner.CA-5/3) * np.log(self.kappa_F * self.kappa_F))
        )
        dsig_vec = np.array([dsig_qg, dsig_qqbar])

        dsig_vec_NLO = np.array([dsig_qg_NLO, dsig_qqbar_NLO])

        Q0 = 37.5
        U_NLL = self.eec_evolver_NLL.get_evolution_operator(Q0, pT, self.R, m=2)
        U_NNLL = self.eec_evolver_NNLL.get_evolution_operator(Q0, pT, self.R, m=4)

        A0_pow = self.eec_evolver_LL.get_operator_fit(Q0, self.R)

        A0_Fit_LL_pow = self.eec_evolver_LL.get_operator_fit(pT, self.R)

        A_NLL_pow = U_NLL @ A0_pow
        A_NNLL_pow = U_NNLL @ A0_pow
        # print(A0_Fit_LL_pow)
        a = dsig_vec[0] * A0_Fit_LL_pow[0][0]
        b = dsig_vec[0] * A0_Fit_LL_pow[0][1]
        c = dsig_vec[1] * A0_Fit_LL_pow[1][0]
        d = dsig_vec[1] * A0_Fit_LL_pow[1][1]

        e = dsig_vec_NLO[0] * A_NLL_pow[0][0]
        f = dsig_vec_NLO[0] * A_NLL_pow[0][1]
        g = dsig_vec_NLO[1] * A_NLL_pow[1][0]
        h = dsig_vec_NLO[1] * A_NLL_pow[1][1]

        i = dsig_vec_NLO[0] * A_NNLL_pow[0][0]
        j = dsig_vec_NLO[0] * A_NNLL_pow[0][1]
        k = dsig_vec_NLO[1] * A_NNLL_pow[1][0]
        l = dsig_vec_NLO[1] * A_NNLL_pow[1][1]

        return np.array(
            [
                a,
                b,
                c,
                d,
                e,
                f,
                g,
                h,
                i,
                j,
                k,
                l,
                dsig_qg,
                dsig_qqbar,
                dsig_qg_NLO,
                dsig_qqbar_NLO,
            ]
        )



class EECFitCalculator:
    def __init__(self, pdf_name: str, ALS_MZ: float):
        print(f"正在初始化截面计算器 (PDF: {pdf_name})...")
        try:
            lhapdf.setVerbosity(0)
            lhapdf.mkPDF(pdf_name)
        except Exception as e:
            print(f"错误: 无法加载 PDF 集 '{pdf_name}'.")
            raise e

        self.alpha_MZ = ALS_MZ
        self.pdf_name = pdf_name
        self.ALZ_runner = AlphaSRunner(ALS_MZ=self.alpha_MZ)
        print("初始化完成。")

    def calculate(
        self,
        Ecom: float,
        radius: float,
        pT_range: Tuple[float, float],
        eta_range: Tuple[float, float],
        scale_factors: Tuple[float, float] = (1.0, 1.0),
        vegas_params: dict = None,
    ) -> vegas.Integrator:
        if vegas_params is None:
            # Increased default neval for better precision in this complex integral
            vegas_params = {"nitn": 10, "neval": 100000}

        print(f"\n开始计算: pT范围 {pT_range} GeV, eta范围 {eta_range}...")
        print(f"标度因子: kappa_R={scale_factors[0]}, kappa_F={scale_factors[1]}")

        integrand = ZJet_EEC_VegasIntegrand(
            pdf_name=self.pdf_name,
            ALZ_runner=self.ALZ_runner,
            Ecom=Ecom,
            pT_range=pT_range,
            eta_range=eta_range,
            R=radius,
            kappa_R=scale_factors[0],
            kappa_F=scale_factors[1],
        )

        integration_bounds = [[0, 1], [0, 1], [0, 1]]
        integ = vegas.Integrator(integration_bounds)

        result = integ(integrand, **vegas_params)

        print("\n--- 计算结束 ---")
        return result


def run_calculation_from_config(config_path: str = "config.yaml"):
    """
    从配置文件运行EEC计算

    Args:
        config_path: 配置文件路径
    """
    # 加载配置
    config_loader = ConfigLoader(config_path)

    # 设置多进程
    mp_config = config_loader.get_multiprocessing_config()
    multiprocessing.set_start_method(
        mp_config['start_method'],
        force=mp_config['force_start_method']
    )

    # 获取配置参数
    pdf_settings = config_loader.get_pdf_settings()
    physics_params = config_loader.get_physics_parameters()
    vegas_params = config_loader.get_vegas_parameters()
    data_config = config_loader.get_data_processing_config()

    height = np.array(data_config['height_array'])
    verbose = data_config['output']['verbose']

    try:
        # 初始化计算器
        calculator = EECFitCalculator(
            pdf_name=pdf_settings['pdf_name'],
            ALS_MZ=pdf_settings['alpha_s_mz']
        )

        # 提取物理参数
        pt_range = (physics_params['pt_range']['min'], physics_params['pt_range']['max'])
        eta_range = (physics_params['eta_range']['min'], physics_params['eta_range']['max'])
        scale_factors = (physics_params['scale_factors']['kappa_r'],
                        physics_params['scale_factors']['kappa_f'])

        if verbose:
            print(f"物理参数:")
            print(f"  质心系能量: {physics_params['center_of_mass_energy']} GeV")
            print(f"  喷注半径: {physics_params['jet_radius']}")
            print(f"  pT范围: {pt_range} GeV")
            print(f"  eta范围: {eta_range}")
            print(f"  标度因子: kappa_R={scale_factors[0]}, kappa_F={scale_factors[1]}")

        # 执行计算
        final_result = calculator.calculate(
            Ecom=physics_params['center_of_mass_energy'],
            radius=physics_params['jet_radius'],
            pT_range=pt_range,
            eta_range=eta_range,
            scale_factors=scale_factors,
            vegas_params=vegas_params,
        )

        result = final_result.flatten()

        if verbose:
            print(f"\n原始结果:")
            print(result)

        # 构建线性方程组并求解
        A_LO = np.array([
            [result[0].mean, result[1].mean],
            [result[2].mean, result[3].mean],
        ])

        A_NLO = np.array([
            [result[8].mean, result[9].mean],
            [result[10].mean, result[11].mean],
        ])

        if verbose:
            print(f"\nLO 系数矩阵 A:")
            print(A_LO)

        if verbose:
            print(f"\nNLO 系数矩阵 A:")
            print(A_NLO)

        B= np.array([height[0]*result[12].mean, height[1]*result[13].mean])

        if verbose:
            print(f"\n右端向量 B:")
            print(B)

        solution_LO = np.linalg.solve(A_LO, B)
        solution_NLO = np.linalg.solve(A_NLO, B)

        print(f"\nLO 最终解:")
        print(solution_LO)

        print(f"\nNLO 最终解:")
        print(solution_NLO)

        # 计算pT区间中点
        pt_center = (pt_range[0] + pt_range[1]) / 2.0

        # 保存结果（如果配置要求）
        if data_config['output']['save_results']:
            output_file = data_config['output']['output_file']

            # 保存简化的结果文件，前面是注释的计算细节，最后一行是关键数据
            with open(output_file, 'w') as f:
                f.write(f"# EEC计算结果文件\n")
                f.write(f"# 配置文件: {config_path}\n")
                f.write(f"# 计算时间: {__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"#\n")
                f.write(f"# 物理参数:\n")
                f.write(f"# 质心系能量: {physics_params['center_of_mass_energy']} GeV\n")
                f.write(f"# 喷注半径: {physics_params['jet_radius']}\n")
                f.write(f"# pT范围: {pt_range[0]}-{pt_range[1]} GeV\n")
                f.write(f"# pT中点: {pt_center} GeV\n")
                f.write(f"# eta范围: {eta_range[0]} to {eta_range[1]}\n")
                f.write(f"# 标度因子: kappa_R={scale_factors[0]}, kappa_F={scale_factors[1]}\n")
                f.write(f"#\n")
                f.write(f"# 计算细节:\n")
                f.write(f"# LO系数矩阵 A_LO:\n")
                f.write(f"# [[{A_LO[0,0]:.8e}, {A_LO[0,1]:.8e}],\n")
                f.write(f"#  [{A_LO[1,0]:.8e}, {A_LO[1,1]:.8e}]]\n")
                f.write(f"# NLO系数矩阵 A_NLO:\n")
                f.write(f"# [[{A_NLO[0,0]:.8e}, {A_NLO[0,1]:.8e}],\n")
                f.write(f"#  [{A_NLO[1,0]:.8e}, {A_NLO[1,1]:.8e}]]\n")
                f.write(f"# 右端向量 B: [{B[0]:.8e}, {B[1]:.8e}]\n")
                f.write(f"# LO拟合解: [{solution_LO[0]:.8e}, {solution_LO[1]:.8e}]\n")
                f.write(f"# NLO拟合解: [{solution_NLO[0]:.8e}, {solution_NLO[1]:.8e}]\n")
                f.write(f"#\n")
                f.write(f"# 原始结果向量 (16个分量):\n")
                for i, val in enumerate(result):
                    f.write(f"# result[{i:2d}] = {val.mean:.8e} +/- {val.sdev:.8e}\n")
                f.write(f"#\n")
                f.write(f"# 最后一行为EEC_EVO_pp.py可读取的数据:\n")
                f.write(f"# 格式: Ecom radius pt_center eta_min eta_max kappa_R kappa_F LO_fit_0 LO_fit_1 NLO_fit_0 NLO_fit_1 result_12 result_13\n")

                # 最后一行：关键参数和结果
                f.write(f"{physics_params['center_of_mass_energy']} {physics_params['jet_radius']} {pt_center} {eta_range[0]} {eta_range[1]} {scale_factors[0]} {scale_factors[1]} {solution_LO[0]:.8e} {solution_LO[1]:.8e} {solution_NLO[0]:.8e} {solution_NLO[1]:.8e}\n")

            print(f"结果已保存到: {output_file}")
            print(f"最后一行包含EEC_EVO_pp.py需要的所有参数")

        return solution_LO, solution_NLO, A_LO, A_NLO, B, result, pt_center

    except Exception as e:
        print(f"\n程序执行时发生错误: {e}")
        import traceback
        traceback.print_exc()
        return None





if __name__ == "__main__":
    import sys

    # 允许从命令行指定配置文件路径
    config_file = sys.argv[1] if len(sys.argv) > 1 else "config.yaml"

    print(f"使用配置文件: {config_file}")
    run_calculation_from_config(config_file)
