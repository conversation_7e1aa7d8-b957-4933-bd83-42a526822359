#!/usr/bin/env python3
"""
EEC_EVO_pp.py 简化命令行参数使用示例
展示如何使用 -M 和 -r 参数以及pT区间设置
"""

import subprocess
import sys
import os

def show_usage_examples():
    """显示使用示例"""
    print("EEC_EVO_pp.py 简化参数使用示例")
    print("=" * 60)
    
    result_file = "test_result.txt"
    
    print("\n1. 基本使用 (使用结果文件中的pT设置)")
    print("   python EEC_EVO_pp.py test_result.txt")
    print("   # 使用默认: alpha_s(MZ)×1.0, 标度因子=1.0")
    
    print("\n2. 调整alpha_s(MZ)")
    print("   python EEC_EVO_pp.py test_result.txt -M 0.90")
    print("   # alpha_s(MZ) = 0.118 × 0.90 = 0.1062")
    
    print("\n3. 调整标度因子")
    print("   python EEC_EVO_pp.py test_result.txt -r 2.0")
    print("   # 标度因子 = 2.0")
    
    print("\n4. 同时调整两个参数")
    print("   python EEC_EVO_pp.py test_result.txt -M 0.90 -r 1.0")
    print("   # alpha_s(MZ)×0.90, 标度因子=1.0")
    
    print("\n5. 设置pT区间 (单个区间)")
    print("   python EEC_EVO_pp.py test_result.txt -M 1.0 -r 1.0 --ptmin 30 --ptmax 60")
    print("   # pT范围: 30-60 GeV, 1个区间")
    
    print("\n6. 设置pT区间 (多个区间)")
    print("   python EEC_EVO_pp.py test_result.txt -M 1.0 -r 1.0 --ptmin 20 --ptmax 100 --pt-bins 4")
    print("   # pT范围: 20-100 GeV, 分为4个区间: 20-40, 40-60, 60-80, 80-100")
    
    print("\n7. 完整示例")
    print("   python EEC_EVO_pp.py test_result.txt -M 0.95 -r 0.5 --ptmin 30 --ptmax 90 --pt-bins 3")
    print("   # alpha_s(MZ)×0.95, 标度因子=0.5, pT: 30-90 GeV分为3个区间")

def show_output_files():
    """显示输出文件命名规则"""
    print("\n" + "=" * 60)
    print("输出文件命名规则")
    print("=" * 60)
    
    examples = [
        ("-M 1.0 -r 1.0", "EEC_EVO_M1.00_r1.0.txt"),
        ("-M 0.90 -r 1.0", "EEC_EVO_M0.90_r1.0.txt"),
        ("-M 1.0 -r 2.0", "EEC_EVO_M1.00_r2.0.txt"),
        ("-M 0.95 -r 0.5", "EEC_EVO_M0.95_r0.5.txt"),
    ]
    
    print("参数组合 → 输出文件名")
    print("-" * 40)
    for params, filename in examples:
        print(f"{params:<20} → {filename}")

def show_parameter_scan():
    """显示参数扫描示例"""
    print("\n" + "=" * 60)
    print("参数扫描示例")
    print("=" * 60)
    
    print("\n1. alpha_s(MZ)扫描:")
    alpha_ratios = [0.8, 0.9, 1.0, 1.1, 1.2]
    for ratio in alpha_ratios:
        print(f"   python EEC_EVO_pp.py test_result.txt -M {ratio} -r 1.0")
        print(f"   # 输出: EEC_EVO_M{ratio:.2f}_r1.0.txt")
    
    print("\n2. 标度因子扫描:")
    scale_factors = [0.5, 1.0, 2.0]
    for factor in scale_factors:
        print(f"   python EEC_EVO_pp.py test_result.txt -M 1.0 -r {factor}")
        print(f"   # 输出: EEC_EVO_M1.00_r{factor:.1f}.txt")
    
    print("\n3. pT区间扫描:")
    pt_configs = [
        ("20", "40", "1"),
        ("40", "60", "1"), 
        ("60", "80", "1"),
        ("20", "80", "3")
    ]
    for ptmin, ptmax, bins in pt_configs:
        print(f"   python EEC_EVO_pp.py test_result.txt -M 1.0 -r 1.0 --ptmin {ptmin} --ptmax {ptmax} --pt-bins {bins}")
        if bins == "1":
            print(f"   # pT区间: {ptmin}-{ptmax} GeV")
        else:
            print(f"   # pT区间: {ptmin}-{ptmax} GeV, 分为{bins}个子区间")

def create_batch_script():
    """创建批处理脚本"""
    print("\n" + "=" * 60)
    print("生成批处理脚本")
    print("=" * 60)
    
    script_content = '''#!/bin/bash
# EEC_EVO_pp.py 简化参数批处理脚本

RESULT_FILE="test_result.txt"

echo "开始EEC演化计算"
echo "================"

# 1. alpha_s(MZ)扫描
echo "1. alpha_s(MZ)扫描"
for M in 0.8 0.9 1.0 1.1 1.2; do
    echo "  运行 -M $M -r 1.0"
    python EEC_EVO_pp.py $RESULT_FILE -M $M -r 1.0
done

# 2. 标度因子扫描  
echo "2. 标度因子扫描"
for r in 0.5 1.0 2.0; do
    echo "  运行 -M 1.0 -r $r"
    python EEC_EVO_pp.py $RESULT_FILE -M 1.0 -r $r
done

# 3. pT区间扫描
echo "3. pT区间扫描"
python EEC_EVO_pp.py $RESULT_FILE -M 1.0 -r 1.0 --ptmin 20 --ptmax 100 --pt-bins 4
echo "  pT区间: 20-100 GeV, 4个子区间"

echo "所有计算完成!"
'''
    
    with open("simple_batch.sh", "w") as f:
        f.write(script_content)
    
    print("批处理脚本已生成: simple_batch.sh")
    print("\n使用方法:")
    print("  chmod +x simple_batch.sh")
    print("  ./simple_batch.sh")

def test_parameter_parsing():
    """测试参数解析"""
    print("\n" + "=" * 60)
    print("参数解析测试")
    print("=" * 60)
    
    test_commands = [
        ["test_result.txt"],
        ["test_result.txt", "-M", "0.9"],
        ["test_result.txt", "-r", "2.0"],
        ["test_result.txt", "-M", "0.9", "-r", "2.0"],
        ["test_result.txt", "--ptmin", "30", "--ptmax", "60"],
        ["test_result.txt", "-M", "0.95", "-r", "0.5", "--ptmin", "20", "--ptmax", "100", "--pt-bins", "4"]
    ]
    
    print("测试命令行参数解析:")
    for i, cmd in enumerate(test_commands, 1):
        print(f"\n{i}. 命令: python EEC_EVO_pp.py {' '.join(cmd[1:])}")
        
        # 这里只是显示命令，不实际执行
        if len(cmd) == 1:
            print("   参数: 默认设置")
        else:
            params = []
            j = 1
            while j < len(cmd):
                if cmd[j].startswith('-'):
                    if j + 1 < len(cmd) and not cmd[j + 1].startswith('-'):
                        params.append(f"{cmd[j]} {cmd[j + 1]}")
                        j += 2
                    else:
                        params.append(cmd[j])
                        j += 1
                else:
                    j += 1
            print(f"   参数: {', '.join(params)}")

def main():
    """主函数"""
    print("EEC_EVO_pp.py 简化使用指南")
    
    # 检查测试文件
    if not os.path.exists("test_result.txt"):
        print("\n注意: 测试文件 test_result.txt 不存在")
        print("请先运行 EEC_Extract.py 生成结果文件")
    
    show_usage_examples()
    show_output_files()
    show_parameter_scan()
    create_batch_script()
    test_parameter_parsing()
    
    print(f"\n{'='*60}")
    print("使用指南完成!")
    print("现在可以根据需要运行相应的命令")
    print(f"{'='*60}")

if __name__ == "__main__":
    main()
