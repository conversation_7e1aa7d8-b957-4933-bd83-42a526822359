# EEC计算工作流程说明

## 概述

这个工作流程展示了如何使用配置文件运行EEC计算，并将结果传递给后续的演化计算程序。

## 工作流程

### 1. 配置EEC_Extract.py

编辑配置文件 `Extract_config/config.yaml`：

```yaml
# 物理参数设置
physics_parameters:
  center_of_mass_energy: 5020
  jet_radius: 0.4
  pt_range:
    min: 40
    max: 60
  eta_range:
    min: -0.5
    max: 0.5
  scale_factors:
    kappa_r: 1.0
    kappa_f: 1.0

# 数据处理参数
data_processing:
  output:
    save_results: true
    output_file: "results.txt"
```

### 2. 运行EEC_Extract.py

```bash
python EEC_Extract.py Extract_config/config.yaml
```

这将生成一个结果文件 `Extract_config/results.txt`，格式如下：

```
# EEC计算结果文件
# 配置文件: Extract_config/config.yaml
# 计算时间: 2024-01-01 12:00:00
#
# 物理参数:
# 质心系能量: 5020 GeV
# 喷注半径: 0.4
# pT范围: 40-60 GeV
# pT中点: 50.0 GeV
# eta范围: -0.5 to 0.5
# 标度因子: kappa_R=1.0, kappa_F=1.0
#
# 计算细节:
# [各种计算细节，都以#注释]
#
# 最后一行为EEC_EVO_pp.py可读取的数据:
# 格式: Ecom radius pt_center eta_min eta_max kappa_R kappa_F LO_fit_0 LO_fit_1 NLO_fit_0 NLO_fit_1 result_12 result_13
5020 0.4 50.0 -0.5 0.5 1.0 1.0 4.60500000e-03 1.18525414e+00 2.27858700e-02 1.05557197e+00 1.23456789e-05 9.87654321e-06
```

### 3. 运行EEC_EVO_pp.py

```bash
python EEC_EVO_pp.py Extract_config/results.txt
```

EEC_EVO_pp.py会自动读取结果文件的最后一行，提取：
- `fit` (LO拟合参数)
- `fit_NLO` (NLO拟合参数)  
- `pt_center` (pT区间中点)
- 其他物理参数

## 输出文件格式说明

结果文件的最后一行包含13个值，按顺序为：

1. `Ecom` - 质心系能量 (GeV)
2. `radius` - 喷注半径
3. `pt_center` - pT区间中点 (GeV)
4. `eta_min` - eta范围最小值
5. `eta_max` - eta范围最大值
6. `kappa_R` - 重整化标度因子
7. `kappa_F` - 因子化标度因子
8. `LO_fit_0` - LO拟合参数第一个分量
9. `LO_fit_1` - LO拟合参数第二个分量
10. `NLO_fit_0` - NLO拟合参数第一个分量
11. `NLO_fit_1` - NLO拟合参数第二个分量
12. `result_12` - 原始结果第12个分量
13. `result_13` - 原始结果第13个分量

## 批量计算

对于多个pT区间的计算：

1. 创建多个配置文件，每个对应不同的pT范围
2. 分别运行EEC_Extract.py生成多个结果文件
3. 依次使用这些结果文件运行EEC_EVO_pp.py

示例：
```bash
# pT范围 20-40 GeV
python EEC_Extract.py config_pt_20_40.yaml

# pT范围 40-60 GeV  
python EEC_Extract.py config_pt_40_60.yaml

# 使用第一个结果运行演化计算
python EEC_EVO_pp.py results_pt_20_40.txt

# 使用第二个结果运行演化计算
python EEC_EVO_pp.py results_pt_40_60.txt
```

## 工具脚本

### workflow_example.py
演示完整工作流程的示例脚本：
```bash
python workflow_example.py
```

### read_eec_results.py
读取和分析结果文件的工具：
```bash
# 显示结果摘要
python read_eec_results.py results.txt summary

# 生成Python输入文件（如果需要）
python read_eec_results.py results.txt generate
```

## 优势

1. **参数传递自动化**: EEC_EVO_pp.py自动从txt文件读取所需参数
2. **pT中点自动计算**: 无需手动计算pT区间中点
3. **完整参数记录**: 结果文件包含所有计算参数，便于追溯
4. **格式统一**: 标准化的输出格式，便于程序间数据传递
5. **批量处理友好**: 支持多个pT区间的批量计算

## 注意事项

1. 确保EEC_Extract.py成功完成并生成有效的结果文件
2. 结果文件的最后一行必须包含13个数值
3. EEC_EVO_pp.py会在找不到结果文件时使用默认参数
4. 建议在运行EEC_EVO_pp.py前检查结果文件的有效性
