#!/usr/bin/env python3
"""
EEC计算工作流程示例
展示从EEC_Extract.py到EEC_EVO_pp.py的完整流程
"""

import os
import sys
import subprocess
import time

def run_eec_extract(config_file="Extract_config/config.yaml"):
    """
    运行EEC_Extract.py计算
    
    Args:
        config_file: 配置文件路径
        
    Returns:
        bool: 是否成功
    """
    print("=" * 60)
    print("步骤1: 运行EEC_Extract.py计算")
    print("=" * 60)
    
    try:
        # 运行EEC_Extract.py
        cmd = [sys.executable, "EEC_Extract.py", config_file]
        print(f"执行命令: {' '.join(cmd)}")
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print("EEC_Extract.py 执行成功")
            print("输出:")
            print(result.stdout)
            return True
        else:
            print("EEC_Extract.py 执行失败")
            print("错误输出:")
            print(result.stderr)
            return False
            
    except subprocess.TimeoutExpired:
        print("EEC_Extract.py 执行超时")
        return False
    except Exception as e:
        print(f"执行EEC_Extract.py时发生错误: {e}")
        return False


def check_result_file(result_file="Extract_config/results.txt"):
    """
    检查结果文件是否存在并显示内容
    
    Args:
        result_file: 结果文件路径
        
    Returns:
        bool: 文件是否存在且有效
    """
    print("\n" + "=" * 60)
    print("步骤2: 检查结果文件")
    print("=" * 60)
    
    if not os.path.exists(result_file):
        print(f"结果文件不存在: {result_file}")
        return False
    
    try:
        with open(result_file, 'r') as f:
            lines = f.readlines()
        
        print(f"结果文件: {result_file}")
        print(f"文件行数: {len(lines)}")
        
        # 显示文件内容
        print("\n文件内容:")
        for i, line in enumerate(lines, 1):
            print(f"{i:3d}: {line.rstrip()}")
        
        # 检查最后一行是否为数据行
        last_line = None
        for line in reversed(lines):
            line = line.strip()
            if line and not line.startswith('#'):
                last_line = line
                break
        
        if last_line:
            values = last_line.split()
            if len(values) == 13:
                print(f"\n✓ 找到有效的数据行: {len(values)} 个值")
                print(f"数据: {last_line}")
                return True
            else:
                print(f"✗ 数据行格式错误: 期望13个值，实际{len(values)}个")
                return False
        else:
            print("✗ 未找到数据行")
            return False
            
    except Exception as e:
        print(f"读取结果文件时发生错误: {e}")
        return False


def run_eec_evo_pp(result_file="Extract_config/results.txt"):
    """
    运行EEC_EVO_pp.py，使用EEC_Extract.py的结果
    
    Args:
        result_file: EEC_Extract.py的结果文件路径
        
    Returns:
        bool: 是否成功
    """
    print("\n" + "=" * 60)
    print("步骤3: 运行EEC_EVO_pp.py")
    print("=" * 60)
    
    try:
        # 运行EEC_EVO_pp.py，传入结果文件路径
        cmd = [sys.executable, "EEC_EVO_pp.py", result_file]
        print(f"执行命令: {' '.join(cmd)}")
        
        # 注意：EEC_EVO_pp.py可能需要很长时间运行，这里只是演示
        print("注意: EEC_EVO_pp.py通常需要很长时间运行")
        print("这里只演示参数读取部分...")
        
        # 只测试参数读取
        from EEC_EVO_pp import read_eec_extract_result
        
        fit, fit_NLO, pt_center, params = read_eec_extract_result(result_file)
        
        print("✓ 成功读取EEC结果文件")
        print(f"LO拟合参数: {fit}")
        print(f"NLO拟合参数: {fit_NLO}")
        print(f"pT中点: {pt_center} GeV")
        print(f"其他参数: {params}")
        
        return True
        
    except Exception as e:
        print(f"运行EEC_EVO_pp.py时发生错误: {e}")
        return False


def main():
    """主函数，演示完整工作流程"""
    print("EEC计算工作流程演示")
    print("从EEC_Extract.py到EEC_EVO_pp.py的完整流程")
    
    # 检查必要文件是否存在
    required_files = ["EEC_Extract.py", "EEC_EVO_pp.py", "Extract_config/config.yaml"]
    missing_files = [f for f in required_files if not os.path.exists(f)]
    
    if missing_files:
        print(f"缺少必要文件: {missing_files}")
        return
    
    start_time = time.time()
    
    # 步骤1: 运行EEC_Extract.py
    if not run_eec_extract():
        print("工作流程中断: EEC_Extract.py执行失败")
        return
    
    # 步骤2: 检查结果文件
    result_file = "Extract_config/results.txt"
    if not check_result_file(result_file):
        print("工作流程中断: 结果文件无效")
        return
    
    # 步骤3: 运行EEC_EVO_pp.py
    if not run_eec_evo_pp(result_file):
        print("工作流程中断: EEC_EVO_pp.py执行失败")
        return
    
    end_time = time.time()
    
    print("\n" + "=" * 60)
    print("工作流程完成!")
    print("=" * 60)
    print(f"总用时: {end_time - start_time:.2f} 秒")
    print(f"结果文件: {result_file}")
    print("\n现在可以使用以下命令运行完整的EEC_EVO_pp.py计算:")
    print(f"python EEC_EVO_pp.py {result_file}")


if __name__ == "__main__":
    main()
