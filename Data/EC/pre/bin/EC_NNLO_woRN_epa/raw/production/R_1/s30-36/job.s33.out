 Input seed: 33                  
 WARNING: init_jet: epem process without JADE/Durham algorithm?!
 set_smin_run: resetting smin
 set_smin_run: smin =    0.0000000000000000     
 registerSweep_run:           2    46291675           2
 set_mass_param: H   125.00000000000000     
 set_width_param: H   4.0296438520000004E-003
 set_mass_param: Z   91.187600000000003     
 set_width_param: Z   2.4952000000000001     
 set_mass_param: W   80.397999999999996     
 set_width_param: W   2.1053999999999999     
 set_mass_param: t   173.19999999999999     
 set_width_param: t   1.4099999999999999     
 set_smin_run: smin =    8.3174400000000014E-013
 registered a jet selector: jets_pt
 set_smin_run: smin =    8.3174400000000014E-013
 set_smin_run: smin =    8.3174400000000014E-013
 nSubF_scl =            1
 logfile: >EPEMJJ.EC_NNLO_woRN_epa.s33.log                                                                                                 <
    _  ___  ____   ____     ____________
   / |/ / |/ / /  / __ \__ / / __/_  __/
  /    /    / /__/ /_/ / // / _/  / /   
 /_/|_/_/|_/____/\____/\___/___/ /_/    
                                        
 ******************************************************************************************************
 *                                                                                                    *
 *  NNLOJET: A multiprocess parton level event generator                                              *
 *                                                                                                    *
 ******************************************************************************************************
 * Active bosons:                                                                                     *
 *   gamma only                                                                                       *
 *                                                                                                    *
 *   > List of active processes <                                                                     *
 *                                                                                                    *
 *   id  partons                  Matrix Element                Subtraction                           *
 *                                                                                                    *
 *    3  ep em to db g d          B1g0Z(3,4,5,2,1)              epemB1g0ZSNLO(3,4,5,2,1)              *
 *    4  ep em to ub g u          B1g0Z(3,4,5,2,1)              epemB1g0ZSNLO(3,4,5,2,1)              *
 *                                                                                                    *
 ******************************************************************************************************
 *                                                                                                    *
 *   > Input technical parameters <                                                                   *
 *                                                                                                    *
 * y0                                                          1.00000000000000002092E-08             *
 * including angular rotations                                                                        *
 * no phase space caching                                                                             *
 *                                                                                                    *
 *                                                                                                    *
 *   > Input physics parameters <                                                                     *
 *                                                                                                    *
 * Z boson mass                                                9.11876000000000033197E+01             *
 * Z boson width                                               2.49520000000000008455E+00             *
 * W boson mass                                                8.03979999999999961346E+01             *
 * W boson width                                               2.10539999999999993818E+00             *
 * H boson mass                                                1.25000000000000000000E+02             *
 * H boson width                                               4.02964385200000035220E-03             *
 * sin^2(thw)                                                  2.22645853412996058696E-01             *
 * 1/alpha                                                     1.32339714389204175404E+02             *
 *                                                                                                    *
 *   > Input kinematical parameters <                                                                 *
 *                                                                                                    *
 * Process: EPEMJJ                                                                                    *
 *     sqrt(s) =   91.2                                                                               *
 *       > beam1 =          -11 [  45.6 GeV]                                                          *
 *       > beam2 =           11 [  45.6 GeV]                                                          *
 *                                                                                                    *
 *     Run: = EC_NNLO_woRN_epa                                                                        *
 *     PDF1 = CT10nlo [mem = 0]                                                                       *
 *     MatchObs = OFF                                                                                 *
 *                                                                                                    *
 *   > Sweeps <                                                                                       *
 *                                                                                                    *
 resetCurrentSweep_run
 loadNextSweep_run:            2
 *       production = 46291675[2]                                                                     *
 *                                                                                                    *
 *   > Warnings <                                                                                     *
 *                                                                                                    *
 *     smin =    0.0                                                                                  *
 *                                                                                                    *
 *   > Scales <                                                                                       *
 *                                                                                                    *
 *   ID                           muR                                     muF                         *
 *   1                  9.120E+01 GeV                           9.120E+01 GeV                         *
 *                                                                                                    *
 *   > Selectors <                                                                                    *
 *                                                                                                    *
 * no selectors registered
 *                                                                                                    *
 *   > Jet settings <                                                                                 *
 *                                                                                                    *
 *   njets:            2 (inclusive)                                                                  *
 *   jet algorithm:   anti-kt    R = 0.70                                                             *
 *   accept:        0.00 <= jets_pt                                                                   *
 *                                                                                                    *
 *   > Histograms <                                                                                   *
 *                                                                                                    *
 * 0 icl                     (      COMPOSITE in    1 bins)     [       0.000,        4.000]          *                              
 *             + jet4              x      34.00                                                       *                                  
 * T          > accept[0]:         20.000 <=   ptj4                                                   *
 *             + jet3              x      32.00                                                       *                                  
 * T          > accept[0]:         20.000 <=   ptj3                                                   *
 *             + jet2              x      30.00                                                       *                                  
 * T          > accept[0]:         20.000 <=   ptj2                                                   *
 *             + jet1              x      28.00                                                       *                                  
 * T          > accept[0]:         20.000 <=   ptj1                                                   *
 *                                                                                                    *
 * 0 EC                      (      COMPOSITE in 1000 bins)     [       0.001,        1.000]          *                              
 *             + dphiJ43           x      26.00                                                       *                                  
 * T          > accept[0]:         20.000 <=   ptj3                                                   *
 *             + dphiJ33           x      24.00                                                       *                                  
 * T          > accept[0]:         20.000 <=   ptj3                                                   *
 *             + dphiJ23           x      22.00                                                       *                                  
 * T          > accept[0]:         20.000 <=   ptj3                                                   *
 *             + dphiJ13           x      19.00                                                       *                                  
 * T          > accept[0]:         20.000 <=   ptj3                                                   *
 *             + dphiJ42           x      17.00                                                       *                                  
 * T          > accept[0]:         20.000 <=   ptj2                                                   *
 *             + dphiJ32           x      15.00                                                       *                                  
 * T          > accept[0]:         20.000 <=   ptj2                                                   *
 *             + dphiJ22           x      13.00                                                       *                                  
 * T          > accept[0]:         20.000 <=   ptj2                                                   *
 *             + dphiJ12           x      10.00                                                       *                                  
 * T          > accept[0]:         20.000 <=   ptj2                                                   *
 *             + dphiJ41           x       8.00                                                       *                                  
 * T          > accept[0]:         20.000 <=   ptj1                                                   *
 *             + dphiJ31           x       6.00                                                       *                                  
 * T          > accept[0]:         20.000 <=   ptj1                                                   *
 *             + dphiJ21           x       4.00                                                       *                                  
 * T          > accept[0]:         20.000 <=   ptj1                                                   *
 *             + dphiJ11           x       1.00                                                       *                                  
 * T          > accept[0]:         20.000 <=   ptj1                                                   *
 *                                                                                                    *
 * 1 cross                   (cross)                                                                  *                              
 *                                                                                                    *
 *                                                                                                    *
 ******************************************************************************************************
 resetCurrentSweep_run
 loadNextSweep_run:            2
 * no selectors registered
 Repository id: 1.0.0
 Maximum available threads:           16
 Allocated number of threads:            1
 resetCurrentSweep_run
 loadNextSweep_run:            2
 
 ------------------------------------------------
 loadNextContrib_chan: loaded R ...
 production...
  > > Entering legacy wrapper for NNLOJET with Vegas! < < 
 Entering New Vegas
  $ OMP active
  $ Maximum number of threads:           16
  $ Number of threads selected:            1
 Reading grid from EPEMJJ.EC_NNLO_woRN_epa.y1.00E-08.R
Commencing iteration n 1
Number of events: 46291675

 > > Current progress: 5%
 > > Current progress: 10%
 > > Current progress: 15%
 > > Current progress: 20%
 > > Current progress: 25%
 > > Current progress: 30%
 > > Current progress: 35%
 > > Current progress: 40%
 > > Current progress: 45%
 > > Current progress: 50%
 > > Current progress: 55%
 > > Current progress: 60%
 > > Current progress: 65%
 > > Current progress: 70%
 > > Current progress: 75%
 > > Current progress: 80%
 > > Current progress: 85%
 > > Current progress: 90%
 > > Current progress: 95%
************* Integration by Vegas (iteration   1) **************
*                                                               *
*  integral  = 0.52801821E-02   accum. integral = 0.52801821E-02*
*  std. dev. = 0.17886384       accum. std. dev = 0.17886384    *
*                                                               *
**************   chi**2/iteration = 0.1084E-16   ****************

Commencing iteration n 2
Number of events: 46291675

 > > Current progress: 5%
 > > Current progress: 10%
 > > Current progress: 15%
 > > Current progress: 20%
 > > Current progress: 25%
 > > Current progress: 30%
 > > Current progress: 35%
 > > Current progress: 40%
 > > Current progress: 45%
 > > Current progress: 50%
 > > Current progress: 55%
 > > Current progress: 60%
 > > Current progress: 65%
 > > Current progress: 70%
 > > Current progress: 75%
 > > Current progress: 80%
 > > Current progress: 85%
 > > Current progress: 90%
 > > Current progress: 95%
************* Integration by Vegas (iteration   2) **************
*                                                               *
*  integral  = -.12380573       accum. integral = -.58623956E-01*
*  std. dev. = 0.18064300       accum. std. dev = 0.12710019    *
*                                                               *
**************   chi**2/iteration = 0.2553       ****************

 writing histogram: EPEMJJ.EC_NNLO_woRN_epa.R.icl.s33.dat
 writing histogram: EPEMJJ.EC_NNLO_woRN_epa.R.EC.s33.dat
 writing histogram: EPEMJJ.EC_NNLO_woRN_epa.R.cross.s33.dat

    qbqb_scale01    0.00000        -0.00%
     qbg_scale01    0.00000        -0.00%
     qbq_scale01  -0.592628E-01   100.00%
     gqb_scale01    0.00000        -0.00%
      gg_scale01    0.00000        -0.00%
      gq_scale01    0.00000        -0.00%
     qqb_scale01    0.00000        -0.00%
      qg_scale01    0.00000        -0.00%
      qq_scale01    0.00000        -0.00%
     tot_scale01  -0.592628E-01 +-   0.127106     fb


 Elapsed time =    38.0123     minutes 
