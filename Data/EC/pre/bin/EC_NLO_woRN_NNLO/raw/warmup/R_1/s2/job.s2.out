 Input seed: 2                   
 WARNING: init_jet: epem process without JADE/Durham algorithm?!
 set_smin_run: resetting smin
 set_smin_run: smin =    0.0000000000000000     
 registerSweep_run:           1        2000           2
 set_scheme_param: ALPHA>FIXED
 >><<
 set_width_param: Z   2.4900000000000002     
 set_mass_param: Z   91.186999999999998     
 set_mass_param: W   80.016267759312541     
 set_smin_run: smin =    8.3174400000000014E-013
 registered a jet selector: jets_pt
 set_smin_run: smin =    1600.0000000000000     
 set_smin_run: smin =    1600.0000000000000     
 nSubF_scl =            1
 parse_hist_io: only warmup run => skipping HISTOGRAMS parsing...
 logfile: >EPEMJJ.EC_NLO_woRN_NNLO.s2.log                                                                                                  <
    _  ___  ____   ____     ____________
   / |/ / |/ / /  / __ \__ / / __/_  __/
  /    /    / /__/ /_/ / // / _/  / /   
 /_/|_/_/|_/____/\____/\___/___/ /_/    
                                        
 ******************************************************************************************************
 *                                                                                                    *
 *  NNLOJET: A multiprocess parton level event generator                                              *
 *                                                                                                    *
 ******************************************************************************************************
 * Active bosons:                                                                                     *
 *   gamma only                                                                                       *
 *                                                                                                    *
 *   > List of active processes <                                                                     *
 *                                                                                                    *
 *   id  partons                  Matrix Element                Subtraction                           *
 *                                                                                                    *
 *    3  ep em to db g d          B1g0Z(3,4,5,2,1)              epemB1g0ZSNLO(3,4,5,2,1)              *
 *    4  ep em to ub g u          B1g0Z(3,4,5,2,1)              epemB1g0ZSNLO(3,4,5,2,1)              *
 *                                                                                                    *
 ******************************************************************************************************
 *                                                                                                    *
 *   > Input technical parameters <                                                                   *
 *                                                                                                    *
 * y0                                                          1.00000000000000002092E-08             *
 * including angular rotations                                                                        *
 * no phase space caching                                                                             *
 *                                                                                                    *
 *                                                                                                    *
 *   > Input physics parameters <                                                                     *
 *                                                                                                    *
 * Z boson mass                                                9.11869999999999976126E+01             *
 * Z boson width                                               2.49000000000000021316E+00             *
 * W boson mass                                                8.00162677593125408748E+01             *
 * W boson width                                               2.08499999999999996447E+00             *
 * H boson mass                                                1.25180000000000006821E+02             *
 * H boson width                                               4.02964385200000035220E-03             *
 * sin^2(thw)                                                  2.29999999999999954481E-01             *
 * 1/alpha                                                     1.27900000000000005684E+02             *
 *                                                                                                    *
 *   > Input kinematical parameters <                                                                 *
 *                                                                                                    *
 * Process: EPEMJJ                                                                                    *
 *     sqrt(s) =   91.2                                                                               *
 *       > beam1 =          -11 [  45.6 GeV]                                                          *
 *       > beam2 =           11 [  45.6 GeV]                                                          *
 *                                                                                                    *
 *     Run: = EC_NLO_woRN_NNLO                                                                        *
 *     PDF1 = CT10nlo [mem = 0]                                                                       *
 *     MatchObs = OFF                                                                                 *
 *                                                                                                    *
 *   > Sweeps <                                                                                       *
 *                                                                                                    *
 resetCurrentSweep_run
 loadNextSweep_run:            1
 *       warmup = 2000[2]                                                                             *
 *                                                                                                    *
 *   > Scales <                                                                                       *
 *                                                                                                    *
 *   ID                           muR                                     muF                         *
 *   1                  1.000E+00 x ptj1                        1.000E+00 x ptj1                      *
 *                                                                                                    *
 *   > Selectors <                                                                                    *
 *                                                                                                    *
 * no selectors registered
 *                                                                                                    *
 *   > Jet settings <                                                                                 *
 *                                                                                                    *
 *   njets:            2 (inclusive)                                                                  *
 *   jet algorithm:   anti-kt    R = 0.70                                                             *
 *   accept:       20.00 <= jets_pt                                                                   *
 *                                                                                                    *
 *   > Histograms <                                                                                   *
 *                                                                                                    *
 *   no histogram registered so far                                                                   *
 *                                                                                                    *
 ******************************************************************************************************
 resetCurrentSweep_run
 loadNextSweep_run:            1
 * no selectors registered
 Repository id: 1.0.0
 Maximum available threads:           16
 Allocated number of threads:            1
 resetCurrentSweep_run
 loadNextSweep_run:            1
 
 ------------------------------------------------
 loadNextContrib_chan: loaded R ...
 warmup...
  > > Entering legacy wrapper for NNLOJET with Vegas! < < 
 Entering New Vegas
  $ OMP active
  $ Maximum number of threads:           16
  $ Number of threads selected:            1
 Reading grid from EPEMJJ.EC_NLO_woRN_NNLO.y1.00E-08.R
Commencing iteration n 1
Number of events: 2000

 > > Current progress: 5%
 > > Current progress: 10%
 > > Current progress: 15%
 > > Current progress: 20%
 > > Current progress: 25%
 > > Current progress: 30%
 > > Current progress: 35%
 > > Current progress: 40%
 > > Current progress: 45%
 > > Current progress: 50%
 > > Current progress: 55%
 > > Current progress: 60%
 > > Current progress: 65%
 > > Current progress: 70%
 > > Current progress: 75%
 > > Current progress: 80%
 > > Current progress: 85%
 > > Current progress: 90%
 > > Current progress: 95%
 > > Current progress: 100%
************* Integration by Vegas (iteration   1) **************
*                                                               *
*  integral  = -37696.621       accum. integral = -37696.621    *
*  std. dev. =  32865.028       accum. std. dev =  32865.028    *
*                                                               *
**************   chi**2/iteration =  0.000       ****************

 Writing grid to EPEMJJ.EC_NLO_woRN_NNLO.y1.00E-08.R
Commencing iteration n 2
Number of events: 2000

 > > Current progress: 5%
 > > Current progress: 10%
 > > Current progress: 15%
 > > Current progress: 20%
 > > Current progress: 25%
 > > Current progress: 30%
 > > Current progress: 35%
 > > Current progress: 40%
 > > Current progress: 45%
 > > Current progress: 50%
 > > Current progress: 55%
 > > Current progress: 60%
 > > Current progress: 65%
 > > Current progress: 70%
 > > Current progress: 75%
 > > Current progress: 80%
 > > Current progress: 85%
 > > Current progress: 90%
 > > Current progress: 95%
 > > Current progress: 100%
************* Integration by Vegas (iteration   2) **************
*                                                               *
*  integral  = -3814.5547       accum. integral = -3822.1027    *
*  std. dev. =  490.58184       accum. std. dev =  490.52719    *
*                                                               *
**************   chi**2/iteration =  1.052       ****************

 Writing grid to EPEMJJ.EC_NLO_woRN_NNLO.y1.00E-08.R

 Elapsed time =   0.230000E-01 seconds 
