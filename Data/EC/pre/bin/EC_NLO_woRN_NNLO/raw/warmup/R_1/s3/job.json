{"exe": "/home/<USER>/Project/HEPLib/NNLOJET/bin/NNLOJET", "mode": 1, "policy": 1, "policy_settings": {"max_runtime": 3600.0}, "ncall": 8000, "niter": 2, "input_files": ["job.run", "EPEMJJ.EC_NLO_woRN_NNLO.y1.00E-08.R_iterations.txt", "EPEMJJ.EC_NLO_woRN_NNLO.y1.00E-08.R"], "output_files": ["EPEMJJ.EC_NLO_woRN_NNLO.s3.log", "job.s3.err", "job.s3.out", "EPEMJJ.EC_NLO_woRN_NNLO.y1.00E-08.R_iterations.txt", "EPEMJJ.EC_NLO_woRN_NNLO.y1.00E-08.R"], "jobs": {"13": {"seed": 3, "iterations": [{"iteration": 1, "result": -3381.1522, "result_acc": -3381.1522, "error": 172.57925, "error_acc": 172.57925, "chi2dof": 0.0}, {"iteration": 2, "result": -3594.7528, "result_acc": -3496.8633, "error": 158.73374, "error_acc": 116.8303, "chi2dof": 0.8216}], "elapsed_time": 0.073, "result": -3496.8633, "error": 116.8303, "chi2dof": 0.8216}}}