{"exe": "/home/<USER>/Project/HEPLib/NNLOJET/bin/NNLOJET", "mode": 1, "policy": 1, "policy_settings": {"max_runtime": 3600.0}, "ncall": 2000, "niter": 2, "input_files": ["job.run", "EPEMJJ.EC_NLO_woRN_NNLO.y1.00E-08.VV", "EPEMJJ.EC_NLO_woRN_NNLO.y1.00E-08.VV_iterations.txt"], "output_files": ["EPEMJJ.EC_NLO_woRN_NNLO.y1.00E-08.VV", "job.s2.out", "EPEMJJ.EC_NLO_woRN_NNLO.y1.00E-08.VV_iterations.txt", "job.s2.err", "EPEMJJ.EC_NLO_woRN_NNLO.s2.log"], "jobs": {"10": {"seed": 2, "iterations": [{"iteration": 1, "result": -159.90274, "result_acc": -159.90274, "error": 0.99562776, "error_acc": 0.99562776, "chi2dof": 0.0}, {"iteration": 2, "result": -160.14264, "result_acc": -160.04762, "error": 0.80634193, "error_acc": 0.62661506, "chi2dof": 0.03471}], "elapsed_time": 0.102, "result": -160.04762, "error": 0.62661506, "chi2dof": 0.03471}}}