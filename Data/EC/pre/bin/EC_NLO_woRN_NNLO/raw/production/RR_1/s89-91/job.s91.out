 Input seed: 91                  
 WARNING: init_jet: epem process without JADE/Durham algorithm?!
 set_smin_run: resetting smin
 set_smin_run: smin =    0.0000000000000000     
 registerSweep_run:           2      216909           2
 set_scheme_param: ALPHA>FIXED
 >><<
 set_width_param: Z   2.4900000000000002     
 set_mass_param: Z   91.186999999999998     
 set_mass_param: W   80.016267759312541     
 set_smin_run: smin =    8.3174400000000014E-013
 registered a jet selector: jets_pt
 set_smin_run: smin =    1600.0000000000000     
 set_smin_run: smin =    1600.0000000000000     
 nSubF_scl =            1
 logfile: >EPEMJJ.EC_NLO_woRN_NNLO.s91.log                                                                                                 <
    _  ___  ____   ____     ____________
   / |/ / |/ / /  / __ \__ / / __/_  __/
  /    /    / /__/ /_/ / // / _/  / /   
 /_/|_/_/|_/____/\____/\___/___/ /_/    
                                        
 ******************************************************************************************************
 *                                                                                                    *
 *  NNLOJET: A multiprocess parton level event generator                                              *
 *                                                                                                    *
 ******************************************************************************************************
 * Active bosons:                                                                                     *
 *   gamma only                                                                                       *
 *                                                                                                    *
 *   > List of active processes <                                                                     *
 *                                                                                                    *
 *   id  partons                  Matrix Element                Subtraction                           *
 *                                                                                                    *
 *    7  ep em to db g g d        B2g0Z(3,4,5,6,2,1)            epemB2g0ZS(3,4,5,6,2,1)               *
 *    8  ep em to ub g g u        B2g0Z(3,4,5,6,2,1)            epemB2g0ZS(3,4,5,6,2,1)               *
 *    9  ep em to db gt gt d      Bt2g0Z(3,4,5,6,2,1)           epemBt2g0ZS(3,4,5,6,2,1)              *
 *   10  ep em to ub gt gt u      Bt2g0Z(3,4,5,6,2,1)           epemBt2g0ZS(3,4,5,6,2,1)              *
 *   11  ep em to db d db d       C0g0Z(3,4,5,6,2,1)            epemC0g0ZS(3,4,5,6,2,1)               *
 *   12  ep em to db u ub d       C0g0Z(3,4,5,6,2,1)            epemC0g0ZS(3,4,5,6,2,1)               *
 *   13  ep em to ub d db u       C0g0Z(3,4,5,6,2,1)            epemC0g0ZS(3,4,5,6,2,1)               *
 *   14  ep em to ub u ub u       C0g0Z(3,4,5,6,2,1)            epemC0g0ZS(3,4,5,6,2,1)               *
 *   15  ep em to db d db d       D0g0Z(3,4,5,6,2,1)            epemD0g0ZS(3,4,5,6,2,1)               *
 *   16  ep em to ub u ub u       D0g0Z(3,4,5,6,2,1)            epemD0g0ZS(3,4,5,6,2,1)               *
 *                                                                                                    *
 ******************************************************************************************************
 *                                                                                                    *
 *   > Input technical parameters <                                                                   *
 *                                                                                                    *
 * y0                                                          1.00000000000000002092E-08             *
 * including angular rotations                                                                        *
 * no phase space caching                                                                             *
 *                                                                                                    *
 *                                                                                                    *
 *   > Input physics parameters <                                                                     *
 *                                                                                                    *
 * Z boson mass                                                9.11869999999999976126E+01             *
 * Z boson width                                               2.49000000000000021316E+00             *
 * W boson mass                                                8.00162677593125408748E+01             *
 * W boson width                                               2.08499999999999996447E+00             *
 * H boson mass                                                1.25180000000000006821E+02             *
 * H boson width                                               4.02964385200000035220E-03             *
 * sin^2(thw)                                                  2.29999999999999954481E-01             *
 * 1/alpha                                                     1.27900000000000005684E+02             *
 *                                                                                                    *
 *   > Input kinematical parameters <                                                                 *
 *                                                                                                    *
 * Process: EPEMJJ                                                                                    *
 *     sqrt(s) =   91.2                                                                               *
 *       > beam1 =          -11 [  45.6 GeV]                                                          *
 *       > beam2 =           11 [  45.6 GeV]                                                          *
 *                                                                                                    *
 *     Run: = EC_NLO_woRN_NNLO                                                                        *
 *     PDF1 = CT10nlo [mem = 0]                                                                       *
 *     MatchObs = OFF                                                                                 *
 *                                                                                                    *
 *   > Sweeps <                                                                                       *
 *                                                                                                    *
 resetCurrentSweep_run
 loadNextSweep_run:            2
 *       production = 216909[2]                                                                       *
 *                                                                                                    *
 *   > Scales <                                                                                       *
 *                                                                                                    *
 *   ID                           muR                                     muF                         *
 *   1                  1.000E+00 x ptj1                        1.000E+00 x ptj1                      *
 *                                                                                                    *
 *   > Selectors <                                                                                    *
 *                                                                                                    *
 * no selectors registered
 *                                                                                                    *
 *   > Jet settings <                                                                                 *
 *                                                                                                    *
 *   njets:            2 (inclusive)                                                                  *
 *   jet algorithm:   anti-kt    R = 0.70                                                             *
 *   accept:       20.00 <= jets_pt                                                                   *
 *                                                                                                    *
 *   > Histograms <                                                                                   *
 *                                                                                                    *
 * 0 EC                      (      COMPOSITE in 1000 bins)     [       0.001,        1.000]          *                              
 *             + dphiJ43           x      24.00                                                       *                                  
 *             + dphiJ33           x      22.00                                                       *                                  
 *             + dphiJ23           x      20.00                                                       *                                  
 *             + dphiJ13           x      18.00                                                       *                                  
 *             + dphiJ42           x      16.00                                                       *                                  
 *             + dphiJ32           x      14.00                                                       *                                  
 *             + dphiJ22           x      12.00                                                       *                                  
 *             + dphiJ12           x      10.00                                                       *                                  
 *             + dphiJ41           x       8.00                                                       *                                  
 *             + dphiJ31           x       6.00                                                       *                                  
 *             + dphiJ21           x       4.00                                                       *                                  
 *             + dphiJ11           x       2.00                                                       *                                  
 *                                                                                                    *
 * 1 cross                   (cross)                                                                  *                              
 *                                                                                                    *
 *                                                                                                    *
 ******************************************************************************************************
 resetCurrentSweep_run
 loadNextSweep_run:            2
 * no selectors registered
 Repository id: 1.0.0
 Maximum available threads:           16
 Allocated number of threads:            1
 resetCurrentSweep_run
 loadNextSweep_run:            2
 
 ------------------------------------------------
 loadNextContrib_chan: loaded RR ...
 production...
  > > Entering legacy wrapper for NNLOJET with Vegas! < < 
 Entering New Vegas
  $ OMP active
  $ Maximum number of threads:           16
  $ Number of threads selected:            1
 Reading grid from EPEMJJ.EC_NLO_woRN_NNLO.y1.00E-08.RR
Commencing iteration n 1
Number of events: 216909

 > > Current progress: 5%
 > > Current progress: 10%
 > > Current progress: 15%
 > > Current progress: 20%
 > > Current progress: 25%
 > > Current progress: 30%
 > > Current progress: 35%
 > > Current progress: 40%
 > > Current progress: 45%
 > > Current progress: 50%
 > > Current progress: 55%
 > > Current progress: 60%
 > > Current progress: 65%
 > > Current progress: 70%
 > > Current progress: 75%
 > > Current progress: 80%
 > > Current progress: 85%
 > > Current progress: 90%
 > > Current progress: 95%
************* Integration by Vegas (iteration   1) **************
*                                                               *
*  integral  = -111.85193       accum. integral = -111.85193    *
*  std. dev. =  150.57895       accum. std. dev =  150.57895    *
*                                                               *
**************   chi**2/iteration =  0.000       ****************

Commencing iteration n 2
Number of events: 216909

 > > Current progress: 5%
 > > Current progress: 10%
 > > Current progress: 15%
 > > Current progress: 20%
 > > Current progress: 25%
 > > Current progress: 30%
 > > Current progress: 35%
 > > Current progress: 40%
 > > Current progress: 45%
 > > Current progress: 50%
 > > Current progress: 55%
 > > Current progress: 60%
 > > Current progress: 65%
 > > Current progress: 70%
 > > Current progress: 75%
 > > Current progress: 80%
 > > Current progress: 85%
 > > Current progress: 90%
 > > Current progress: 95%
************* Integration by Vegas (iteration   2) **************
*                                                               *
*  integral  = -226.01600       accum. integral = -156.40037    *
*  std. dev. =  188.23543       accum. std. dev =  117.58527    *
*                                                               *
**************   chi**2/iteration = 0.2221       ****************

 writing histogram: EPEMJJ.EC_NLO_woRN_NNLO.RR.EC.s91.dat
 writing histogram: EPEMJJ.EC_NLO_woRN_NNLO.RR.cross.s91.dat

    qbqb_scale01    0.00000        -0.00%
     qbg_scale01    0.00000        -0.00%
     qbq_scale01   -168.934       100.00%
     gqb_scale01    0.00000        -0.00%
      gg_scale01    0.00000        -0.00%
      gq_scale01    0.00000        -0.00%
     qqb_scale01    0.00000        -0.00%
      qg_scale01    0.00000        -0.00%
      qq_scale01    0.00000        -0.00%
     tot_scale01   -168.934     +-    120.526     fb


 Elapsed time =    27.2050     minutes 
